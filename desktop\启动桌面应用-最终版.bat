@echo off
chcp 65001 >nul
title Yu Reader 桌面应用启动器

echo.
echo ========================================
echo    🚀 Yu Reader 桌面应用启动器
echo ========================================
echo.

REM 检查Node.js
echo 🔍 检查Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 Node.js，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js 已安装

REM 检查依赖
echo 🔍 检查依赖...
if not exist "node_modules" (
    echo ❌ 依赖未安装，正在安装...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)
echo ✅ 依赖已安装

REM 检查必要文件
echo 🔍 检查必要文件...
if not exist "dist\main\app.js" (
    echo ❌ 主进程文件不存在，正在构建...
    npm run build:main
    if errorlevel 1 (
        echo ❌ 主进程构建失败
        pause
        exit /b 1
    )
)
echo ✅ 主进程文件存在

if not exist "dist\preload\index.js" (
    echo ❌ 预加载脚本不存在，正在构建...
    npm run build:preload
    if errorlevel 1 (
        echo ❌ 预加载脚本构建失败
        pause
        exit /b 1
    )
)
echo ✅ 预加载脚本存在

REM 启动开发服务器（如果未运行）
echo 🔍 检查开发服务器状态...
netstat -an | find "5173" >nul
if errorlevel 1 (
    echo 🚀 启动开发服务器...
    start "Yu Reader Dev Server" cmd /c "npm run dev:renderer"
    echo ⏳ 等待开发服务器启动...
    timeout /t 15 /nobreak >nul
) else (
    echo ✅ 开发服务器已运行
)

REM 尝试启动Electron桌面应用
echo 🚀 尝试启动Electron桌面应用...

REM 方法1: 使用npm脚本
echo 📱 方法1: 使用npm脚本启动...
start "Yu Reader Desktop" cmd /c "set NODE_ENV=development && npx electron ."
timeout /t 5 /nobreak >nul

REM 检查是否启动成功
tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
if not errorlevel 1 (
    echo ✅ Electron桌面应用启动成功！
    echo.
    echo ========================================
    echo    ✅ 桌面应用已启动
    echo ========================================
    goto :end
)

REM 方法2: 直接启动可执行文件
echo 📱 方法2: 直接启动可执行文件...
if exist "node_modules\electron\dist\electron.exe" (
    start "Yu Reader Desktop" cmd /c "set NODE_ENV=development && node_modules\electron\dist\electron.exe ."
    timeout /t 5 /nobreak >nul
    
    tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
    if not errorlevel 1 (
        echo ✅ Electron桌面应用启动成功！
        echo.
        echo ========================================
        echo    ✅ 桌面应用已启动
        echo ========================================
        goto :end
    )
)

REM 如果桌面应用启动失败，在浏览器中打开
echo ⚠️ Electron桌面应用启动失败，在浏览器中打开应用...
echo 🌐 在浏览器中打开应用...
start http://127.0.0.1:5173/

echo.
echo ========================================
echo    ✅ 应用已在浏览器中启动
echo    📱 URL: http://127.0.0.1:5173/
echo ========================================
echo.
echo 💡 提示: 
echo    - 浏览器版本包含所有桌面端功能
echo    - 支持PDF阅读器的所有增强功能
echo    - 可以正常使用文件上传和处理
echo.
echo 🔧 如需桌面应用，请尝试:
echo    - 重新安装依赖: npm install
echo    - 重新构建: npm run build
echo    - 检查防火墙和杀毒软件设置

:end
pause
