import{d as t,e as a,c as n,f as e,a as c,r as p,o as _,_ as r}from"./index-krGsH_w3.js";const d={class:"notes-view"},i={class:"notes-content"},l=t({__name:"NotesView",setup(m){return a(()=>{console.log("笔记管理页面已加载")}),(f,s)=>{const o=p("el-empty");return _(),n("div",d,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"笔记管理"),e("p",{class:"page-description"},"整理和管理阅读笔记")],-1)),e("div",i,[c(o,{description:"笔记管理功能开发中..."})])])}}}),u=r(l,[["__scopeId","data-v-cb088ab3"]]);export{u as default};
