@echo off
chcp 65001 >nul
title Yu Reader 桌面端启动器

echo.
echo ========================================
echo    🚀 Yu Reader 桌面端启动器
echo    确保成功版本
echo ========================================
echo.

REM 设置环境变量
set NODE_ENV=development

echo 🔍 当前目录: %CD%
echo 🔍 环境变量: NODE_ENV=%NODE_ENV%

REM 检查开发服务器
echo.
echo 🌐 检查开发服务器状态...
netstat -an | find "5173" >nul
if errorlevel 1 (
    echo ⚠️ 开发服务器未运行
    echo 💡 正在启动开发服务器...
    start "Yu Reader Dev Server" cmd /c "cd /d %CD% && npm run dev:renderer"
    echo ⏳ 等待开发服务器启动...
    timeout /t 10 /nobreak >nul
) else (
    echo ✅ 开发服务器已运行 (端口5173)
)

REM 检查必要文件
echo.
echo 📁 检查必要文件...
if not exist "dist\main\app.js" (
    echo ❌ 主进程文件不存在
    echo 💡 正在构建主进程...
    call npm run build:main
    if errorlevel 1 (
        echo ❌ 主进程构建失败
        goto :browser_fallback
    )
    echo ✅ 主进程构建完成
) else (
    echo ✅ 主进程文件存在
)

if not exist "dist\preload\index.js" (
    echo ❌ 预加载文件不存在
    echo 💡 正在构建预加载脚本...
    call npm run build:preload
    if errorlevel 1 (
        echo ❌ 预加载脚本构建失败
        goto :browser_fallback
    )
    echo ✅ 预加载脚本构建完成
) else (
    echo ✅ 预加载文件存在
)

REM 启动桌面应用
echo.
echo 🚀 启动桌面应用...

REM 方法1: 使用npm脚本
echo 📱 尝试方法1: npm run start:dev
start "Yu Reader Desktop" cmd /c "cd /d %CD% && echo 正在启动Yu Reader桌面应用... && npm run start:dev && echo 应用已退出 && pause"

REM 等待5秒检查
timeout /t 5 /nobreak >nul

REM 检查是否启动成功
tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
if not errorlevel 1 (
    echo ✅ 桌面应用启动成功！
    echo 🎉 Yu Reader 桌面应用正在运行
    goto :success
)

REM 方法2: 直接使用npx
echo 📱 尝试方法2: npx electron
start "Yu Reader Desktop" cmd /c "cd /d %CD% && echo 正在启动Yu Reader桌面应用... && set NODE_ENV=development && npx electron . && echo 应用已退出 && pause"

REM 等待5秒检查
timeout /t 5 /nobreak >nul

tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
if not errorlevel 1 (
    echo ✅ 桌面应用启动成功！
    echo 🎉 Yu Reader 桌面应用正在运行
    goto :success
)

REM 方法3: 使用node启动脚本
echo 📱 尝试方法3: Node.js启动脚本
start "Yu Reader Desktop" cmd /c "cd /d %CD% && echo 正在启动Yu Reader桌面应用... && node start-desktop-app.js && pause"

REM 等待5秒检查
timeout /t 5 /nobreak >nul

tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
if not errorlevel 1 (
    echo ✅ 桌面应用启动成功！
    echo 🎉 Yu Reader 桌面应用正在运行
    goto :success
)

:browser_fallback
echo.
echo ⚠️ 桌面应用启动失败，使用浏览器版本
echo 🌐 在浏览器中打开应用...
start http://127.0.0.1:5173/

echo.
echo ========================================
echo    ✅ 应用已在浏览器中启动
echo    📱 URL: http://127.0.0.1:5173/
echo ========================================
echo.
echo 💡 浏览器版本功能完整:
echo    - 包含所有桌面端功能
echo    - PDF阅读器增强功能
echo    - 智能错误处理和性能优化
echo    - 内存管理和监控面板
echo    - 支持文件上传和处理
echo.
goto :end

:success
echo.
echo ========================================
echo    ✅ 桌面应用启动成功！
echo ========================================
echo.
echo 🎉 Yu Reader 桌面应用功能:
echo    - 完整的PDF阅读器增强功能
echo    - 智能错误处理和恢复建议
echo    - 大文件性能优化
echo    - 实时内存管理和监控
echo    - 点击📊图标查看性能面板
echo.
echo 💡 如果没有看到窗口:
echo    - 检查任务栏是否有应用图标
echo    - 使用Alt+Tab切换窗口
echo    - 检查系统托盘区域
echo.

:end
echo 按任意键退出启动器...
pause >nul
