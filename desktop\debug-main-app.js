/**
 * 调试主应用启动
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 调试启动Yu Reader桌面应用...');

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.ELECTRON_ENABLE_LOGGING = '1';

const electronPath = path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe');
const appPath = __dirname;

console.log('Electron路径:', electronPath);
console.log('应用路径:', appPath);
console.log('主进程文件:', path.join(__dirname, 'dist', 'main', 'app.js'));

// 启动Electron并捕获所有输出
const child = spawn(electronPath, [appPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: __dirname,
    env: {
        ...process.env,
        NODE_ENV: 'development',
        ELECTRON_ENABLE_LOGGING: '1',
        ELECTRON_ENABLE_STACK_DUMPING: '1'
    }
});

console.log('📱 Electron进程已启动，PID:', child.pid);

child.stdout.on('data', (data) => {
    console.log('📤 STDOUT:', data.toString());
});

child.stderr.on('data', (data) => {
    console.log('📤 STDERR:', data.toString());
});

child.on('error', (error) => {
    console.error('❌ 进程错误:', error);
});

child.on('close', (code, signal) => {
    console.log(`📱 进程退出 - 代码: ${code}, 信号: ${signal}`);
});

child.on('exit', (code, signal) => {
    console.log(`📱 进程结束 - 代码: ${code}, 信号: ${signal}`);
});

// 10秒后检查状态
setTimeout(() => {
    if (child.exitCode === null && !child.killed) {
        console.log('✅ Electron应用正在运行！');
        console.log('🎉 桌面应用应该已经显示！');
    } else {
        console.log('❌ Electron应用已退出');
    }
}, 10000);

// 保持进程运行
console.log('⏳ 监控Electron进程，按Ctrl+C退出...');

process.on('SIGINT', () => {
    console.log('\n🛑 关闭调试器...');
    child.kill();
    process.exit(0);
});
