const { app, BrowserWindow, ipcMain } = require('electron')
const { join } = require('path')

console.log('🔄 Yu Reader 混合模式启动...')

let mainWindow = null
let useFullDatabase = false

// 尝试加载完整的主进程应用
async function tryLoadFullApp() {
  try {
    console.log('🔍 尝试加载完整主进程应用...')
    
    // 检查主应用文件是否存在
    const fs = require('fs')
    const mainAppPath = join(__dirname, 'dist/main/app.js')
    
    if (!fs.existsSync(mainAppPath)) {
      console.log('⚠️ 主应用文件不存在，使用简化模式')
      return false
    }
    
    console.log('✅ 主应用文件存在，尝试初始化完整功能...')
    
    // 这里可以尝试初始化数据库等服务
    // 暂时返回false，使用简化模式
    return false
    
  } catch (error) {
    console.error('❌ 加载完整应用失败:', error.message)
    return false
  }
}

// 注册IPC处理器 - 混合模式
function registerHybridIPCHandlers() {
  console.log('📡 注册混合模式IPC处理器...')
  
  // 图书列表处理器 - 混合模式
  ipcMain.handle('book:list', async () => {
    console.log('📚 收到图书列表请求 (混合模式)')
    
    if (useFullDatabase) {
      try {
        console.log('🗄️ 尝试从数据库获取图书...')
        // 这里应该调用真实的数据库查询
        // const books = await databaseManager.getAllBooks()
        // return books
        
        // 暂时模拟数据库查询失败
        throw new Error('数据库连接失败')
      } catch (error) {
        console.error('❌ 数据库查询失败，降级到示例数据:', error.message)
        useFullDatabase = false
      }
    }
    
    // 返回示例数据
    console.log('📖 返回示例图书数据...')
    const sampleBooks = [
      {
        id: '1',
        title: '混合模式示例 - 《三体》',
        author: '刘慈欣',
        file_format: 'epub',
        format: 'epub',
        file_size: 3145728,
        fileSize: 3145728,
        description: '科幻小说经典之作，探索宇宙文明的宏大史诗。',
        reading_status: 'reading',
        readingStatus: 'reading',
        current_page: 156,
        currentPage: 156,
        reading_progress_percent: 45,
        readProgress: 45,
        created_at: new Date('2024-01-15').toISOString(),
        createdAt: new Date('2024-01-15').toISOString(),
        updated_at: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        addedAt: new Date('2024-01-15').toISOString(),
        lastReadAt: new Date().toISOString(),
        tags: ['科幻', '经典', '中国'],
        language: 'zh-CN',
        publisher: '重庆出版社',
        publishDate: '2008-01-01',
        isbn: '978-7536692930',
        coverPath: null,
        filePath: '/books/三体.epub'
      },
      {
        id: '2',
        title: '混合模式示例 - 《活着》',
        author: '余华',
        file_format: 'txt',
        format: 'txt',
        file_size: 512000,
        fileSize: 512000,
        description: '现代文学经典，讲述一个人和他命运之间的友情。',
        reading_status: 'finished',
        readingStatus: 'finished',
        current_page: 200,
        currentPage: 200,
        reading_progress_percent: 100,
        readProgress: 100,
        created_at: new Date('2024-02-01').toISOString(),
        createdAt: new Date('2024-02-01').toISOString(),
        updated_at: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        addedAt: new Date('2024-02-01').toISOString(),
        lastReadAt: new Date('2024-07-20').toISOString(),
        tags: ['文学', '现代', '中国'],
        language: 'zh-CN',
        publisher: '作家出版社',
        publishDate: '1993-01-01',
        isbn: '978-7506365437',
        coverPath: null,
        filePath: '/books/活着.txt'
      },
      {
        id: '3',
        title: '混合模式示例 - 《JavaScript高级程序设计》',
        author: 'Nicholas C. Zakas',
        file_format: 'pdf',
        format: 'pdf',
        file_size: 10485760,
        fileSize: 10485760,
        description: 'JavaScript开发者必读的经典技术书籍。',
        reading_status: 'unread',
        readingStatus: 'unread',
        current_page: 0,
        currentPage: 0,
        reading_progress_percent: 0,
        readProgress: 0,
        created_at: new Date('2024-03-01').toISOString(),
        createdAt: new Date('2024-03-01').toISOString(),
        updated_at: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        addedAt: new Date('2024-03-01').toISOString(),
        lastReadAt: null,
        tags: ['技术', 'JavaScript', '编程'],
        language: 'zh-CN',
        publisher: '人民邮电出版社',
        publishDate: '2020-01-01',
        isbn: '978-7115537041',
        coverPath: null,
        filePath: '/books/JavaScript高级程序设计.pdf'
      },
      {
        id: '4',
        title: '混合模式示例 - 《百年孤独》',
        author: '加西亚·马尔克斯',
        file_format: 'epub',
        format: 'epub',
        file_size: 2097152,
        fileSize: 2097152,
        description: '魔幻现实主义文学的代表作品。',
        reading_status: 'reading',
        readingStatus: 'reading',
        current_page: 89,
        currentPage: 89,
        reading_progress_percent: 25,
        readProgress: 25,
        created_at: new Date('2024-04-01').toISOString(),
        createdAt: new Date('2024-04-01').toISOString(),
        updated_at: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        addedAt: new Date('2024-04-01').toISOString(),
        lastReadAt: new Date('2024-07-30').toISOString(),
        tags: ['文学', '魔幻现实主义', '拉美'],
        language: 'zh-CN',
        publisher: '南海出版公司',
        publishDate: '2011-06-01',
        isbn: '978-7544253994',
        coverPath: null,
        filePath: '/books/百年孤独.epub'
      }
    ]
    
    console.log(`📚 返回 ${sampleBooks.length} 本混合模式图书:`)
    sampleBooks.forEach((book, index) => {
      console.log(`  ${index + 1}. ${book.title} (${book.format}) - ${book.readingStatus}`)
    })
    
    return sampleBooks
  })
  
  // 其他基本IPC处理器
  ipcMain.handle('book:get', async (_, bookId) => {
    console.log('📖 收到获取单本图书请求:', bookId)
    return null // 简化模式不支持
  })
  
  ipcMain.handle('book:add', async (_, filePath) => {
    console.log('➕ 收到添加图书请求:', filePath)
    return { success: false, message: '混合模式不支持添加图书' }
  })
  
  // 数据库处理器
  ipcMain.handle('database:check-book-paths', async () => {
    console.log('🔍 收到数据库路径检查请求')
    return { success: true, message: '混合模式跳过数据库检查' }
  })
  
  ipcMain.handle('database:fix-book-paths', async () => {
    console.log('🔧 收到数据库路径修复请求')
    return { success: true, message: '混合模式跳过数据库修复' }
  })
  
  // 窗口操作处理器
  ipcMain.handle('window:minimize', async () => {
    if (mainWindow) mainWindow.minimize()
    return { success: true }
  })
  
  ipcMain.handle('window:maximize', async () => {
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize()
      } else {
        mainWindow.maximize()
      }
    }
    return { success: true }
  })
  
  ipcMain.handle('window:close', async () => {
    if (mainWindow) mainWindow.close()
    return { success: true }
  })
  
  console.log('✅ 混合模式IPC处理器注册完成')
}

function createWindow() {
  console.log('🪟 创建混合模式窗口...')
  
  const { screen } = require('electron')
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize
  
  const windowWidth = Math.min(1200, Math.floor(width * 0.8))
  const windowHeight = Math.min(800, Math.floor(height * 0.8))
  
  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: 800,
    minHeight: 600,
    show: true,
    frame: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, 'dist/preload/index.js'),
      webSecurity: false,
      allowRunningInsecureContent: true,
      sandbox: false,
      spellcheck: false
    }
  })

  // 优先使用开发服务器
  const devUrl = 'http://127.0.0.1:5173'
  console.log('🌐 加载开发服务器:', devUrl)
  
  mainWindow.loadURL(devUrl).catch(err => {
    console.error('❌ 开发服务器加载失败:', err)
    const buildFile = join(__dirname, 'dist/renderer/index.html')
    console.log('🔄 降级到构建文件:', buildFile)
    mainWindow.loadFile(buildFile)
  })
  
  // 开发模式下打开开发者工具
  mainWindow.webContents.openDevTools()

  mainWindow.on('closed', () => {
    console.log('🪟 主窗口已关闭')
    mainWindow = null
  })

  mainWindow.once('ready-to-show', () => {
    console.log('✨ 窗口准备显示')
    mainWindow.show()
    mainWindow.focus()
  })

  console.log('✅ 混合模式窗口创建完成')
  return mainWindow
}

app.whenReady().then(async () => {
  console.log('⚡ Electron 应用已准备就绪')
  
  try {
    // 尝试加载完整功能
    useFullDatabase = await tryLoadFullApp()
    
    if (useFullDatabase) {
      console.log('🎯 使用完整数据库模式')
    } else {
      console.log('🎯 使用简化示例数据模式')
    }
    
    // 注册IPC处理器
    registerHybridIPCHandlers()
    
    // 创建窗口
    createWindow()
    
    console.log('🚀 混合模式应用启动成功')
  } catch (error) {
    console.error('💥 应用启动失败:', error)
    app.quit()
  }
})

app.on('window-all-closed', () => {
  console.log('🚪 所有窗口已关闭')
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的 Promise 拒绝:', reason)
})

console.log('📋 混合启动脚本已加载')
