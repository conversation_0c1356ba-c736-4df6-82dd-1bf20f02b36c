@echo off
chcp 65001 >nul
title Yu Reader 桌面应用

echo 🚀 启动Yu Reader桌面应用...

REM 设置环境变量
set NODE_ENV=development

REM 检查必要文件
if not exist "dist\main\app.js" (
    echo ❌ 主进程文件不存在
    pause
    exit /b 1
)

if not exist "node_modules\electron\dist\electron.exe" (
    echo ❌ Electron可执行文件不存在
    pause
    exit /b 1
)

echo ✅ 文件检查通过
echo 📱 启动Electron应用...

REM 启动Electron
start "Yu Reader" "node_modules\electron\dist\electron.exe" "."

echo ✅ 应用启动命令已执行
echo 💡 如果应用没有显示，请检查是否被防火墙或杀毒软件阻止

timeout /t 3 /nobreak >nul
