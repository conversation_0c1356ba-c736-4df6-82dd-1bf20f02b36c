/**
 * 直接启动Electron桌面应用
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 直接启动Electron桌面应用...');

// 设置环境变量
process.env.NODE_ENV = 'development';

const electronPath = path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe');
const appPath = __dirname;

console.log('Electron路径:', electronPath);
console.log('应用路径:', appPath);

// 直接启动Electron
const child = spawn(electronPath, [appPath], {
    stdio: 'inherit',
    env: {
        ...process.env,
        NODE_ENV: 'development'
    },
    detached: true
});

child.unref(); // 让子进程独立运行

console.log('✅ Electron已启动，PID:', child.pid);
console.log('🎉 桌面应用应该已经显示！');

// 等待3秒后退出启动器
setTimeout(() => {
    console.log('📱 启动器退出，Electron继续运行');
    process.exit(0);
}, 3000);
