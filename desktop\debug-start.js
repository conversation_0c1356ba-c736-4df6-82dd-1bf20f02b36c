const { app, BrowserWindow, ipcMain } = require('electron')
const { join } = require('path')

console.log('🔍 Yu Reader 调试启动模式...')

let mainWindow = null

// 基本IPC处理器（防止IPC错误）
function registerBasicIPCHandlers() {
  console.log('注册基本IPC处理器...')
  
  ipcMain.handle('book:list', async () => {
    console.log('收到图书列表请求')
    return []
  })
  
  ipcMain.handle('database:check-book-paths', async () => {
    console.log('收到数据库路径检查请求')
    return { success: true }
  })
  
  ipcMain.handle('database:fix-book-paths', async () => {
    console.log('收到数据库路径修复请求')
    return { success: true }
  })
  
  console.log('基本IPC处理器注册完成')
}

// 尝试加载完整的主进程应用
async function tryFullApp() {
  try {
    console.log('🔄 尝试加载完整主进程应用...')
    
    // 动态导入主应用
    const mainAppPath = join(__dirname, 'dist/main/app.js')
    console.log('主应用路径:', mainAppPath)
    
    // 检查文件是否存在
    const fs = require('fs')
    if (!fs.existsSync(mainAppPath)) {
      throw new Error('主应用文件不存在: ' + mainAppPath)
    }
    
    console.log('主应用文件存在，尝试加载...')
    
    // 这里不直接require，而是让Electron自己处理
    return false // 暂时返回false，使用简化模式
    
  } catch (error) {
    console.error('加载完整主进程失败:', error.message)
    return false
  }
}

function createWindow() {
  console.log('创建调试窗口...')
  
  const { screen } = require('electron')
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize
  
  const windowWidth = Math.min(1200, Math.floor(width * 0.8))
  const windowHeight = Math.min(800, Math.floor(height * 0.8))
  
  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: 800,
    minHeight: 600,
    show: true,
    frame: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, 'dist/preload/index.js'),
      webSecurity: false,
      allowRunningInsecureContent: true,
      sandbox: false,
      spellcheck: false
    }
  })

  // 检查环境变量
  const nodeEnv = (process.env.NODE_ENV || '').trim()
  console.log('环境变量 NODE_ENV:', `"${nodeEnv}"`)
  
  if (nodeEnv === 'development') {
    const devUrl = 'http://127.0.0.1:5173'
    console.log('开发模式，加载URL:', devUrl)
    
    mainWindow.loadURL(devUrl).catch(err => {
      console.error('加载开发服务器失败:', err)
      const buildFile = join(__dirname, 'dist/renderer/index.html')
      console.log('尝试加载构建文件:', buildFile)
      mainWindow.loadFile(buildFile)
    })
    
    // 开发模式下打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    const buildFile = join(__dirname, 'dist/renderer/index.html')
    console.log('生产模式，加载文件:', buildFile)
    mainWindow.loadFile(buildFile)
  }

  mainWindow.on('closed', () => {
    console.log('主窗口已关闭')
    mainWindow = null
  })

  mainWindow.once('ready-to-show', () => {
    console.log('窗口准备显示')
    mainWindow.show()
    mainWindow.focus()
  })

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('页面加载失败:', errorCode, errorDescription, validatedURL)
  })

  console.log('调试窗口创建完成')
  return mainWindow
}

app.whenReady().then(async () => {
  console.log('Electron 应用已准备就绪')
  
  try {
    // 注册基本IPC处理器
    registerBasicIPCHandlers()
    
    // 尝试使用完整功能
    const useFullApp = await tryFullApp()
    
    if (useFullApp) {
      console.log('✅ 使用完整功能模式')
      // 这里应该启动完整的主进程应用
    } else {
      console.log('⚠️ 使用调试简化模式')
      createWindow()
    }
    
    console.log('应用启动成功')
  } catch (error) {
    console.error('应用启动失败:', error)
    app.quit()
  }
})

app.on('window-all-closed', () => {
  console.log('所有窗口已关闭')
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason)
})

console.log('调试启动脚本已加载')
