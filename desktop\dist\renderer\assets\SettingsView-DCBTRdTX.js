import{d as W,cF as A,cc as T,b as E,bn as b,e as I,c as N,f as t,a as n,w as s,r as p,l as u,t as m,m as o,o as P,_ as M}from"./index-krGsH_w3.js";const L={class:"settings-view"},Y={class:"settings-content"},D={class:"setting-section"},R={class:"setting-section"},j={class:"setting-item"},q={class:"setting-item"},G={class:"setting-item"},J={class:"setting-section"},K={class:"setting-item"},O={class:"setting-section"},Q={class:"setting-item"},X={class:"setting-item"},Z={class:"setting-section"},$={class:"about-section"},ee={class:"system-info"},te=W({__name:"SettingsView",setup(le){const d=A(),w=T(),y=E("appearance"),l=b({...d.settings}),g=b({platform:"",version:""}),c=async()=>{try{await w.setTheme(l.theme),await d.updateSettings({theme:l.theme}),o.success("主题设置已保存")}catch(r){console.error("主题设置失败:",r),o.error("保存失败")}},x=async()=>{try{await d.updateSettings({fontSize:l.fontSize}),o.success("字体大小已保存")}catch{o.error("保存失败")}},C=async()=>{try{await d.updateSettings({fontFamily:l.fontFamily}),o.success("字体设置已保存")}catch{o.error("保存失败")}},U=async()=>{try{await d.updateSettings({lineHeight:l.lineHeight}),o.success("行高设置已保存")}catch{o.error("保存失败")}},F=async()=>{try{await d.updateSettings({pageWidth:l.pageWidth}),o.success("页面宽度已保存")}catch{o.error("保存失败")}},h=async()=>{try{await d.updateSettings({autoSave:l.autoSave}),o.success("自动保存设置已保存")}catch{o.error("保存失败")}},k=async()=>{try{await d.updateSettings({autoBackup:l.autoBackup}),o.success("自动备份设置已保存")}catch{o.error("保存失败")}},B=async()=>{try{await d.updateSettings({language:l.language}),o.success("语言设置已保存")}catch{o.error("保存失败")}};return I(()=>{g.platform=window.electronAPI.system.platform,g.version=window.electronAPI.system.version}),(r,e)=>{const i=p("el-radio"),V=p("el-radio-group"),_=p("el-slider"),v=p("el-option"),z=p("el-select"),f=p("el-tab-pane"),S=p("el-switch"),H=p("el-tabs");return P(),N("div",L,[e[32]||(e[32]=t("div",{class:"page-header"},[t("h1",null,"设置"),t("p",null,"个性化您的阅读体验")],-1)),t("div",Y,[n(H,{modelValue:y.value,"onUpdate:modelValue":e[8]||(e[8]=a=>y.value=a),class:"settings-tabs"},{default:s(()=>[n(f,{label:"外观",name:"appearance"},{default:s(()=>[t("div",D,[e[17]||(e[17]=t("h3",null,"主题",-1)),n(V,{modelValue:l.theme,"onUpdate:modelValue":e[0]||(e[0]=a=>l.theme=a),onChange:c},{default:s(()=>[n(i,{value:"light"},{default:s(()=>e[9]||(e[9]=[u("明亮主题")])),_:1,__:[9]}),n(i,{value:"dark"},{default:s(()=>e[10]||(e[10]=[u("深色主题")])),_:1,__:[10]}),n(i,{value:"eye-care"},{default:s(()=>e[11]||(e[11]=[u("护眼主题")])),_:1,__:[11]}),n(i,{value:"eye-care-warm"},{default:s(()=>e[12]||(e[12]=[u("护眼暖色主题")])),_:1,__:[12]}),n(i,{value:"high-contrast"},{default:s(()=>e[13]||(e[13]=[u("高对比度主题")])),_:1,__:[13]}),n(i,{value:"night"},{default:s(()=>e[14]||(e[14]=[u("夜间模式")])),_:1,__:[14]}),n(i,{value:"natural"},{default:s(()=>e[15]||(e[15]=[u("自然模式")])),_:1,__:[15]}),n(i,{value:"auto"},{default:s(()=>e[16]||(e[16]=[u("跟随系统")])),_:1,__:[16]})]),_:1},8,["modelValue"])]),t("div",R,[e[21]||(e[21]=t("h3",null,"字体设置",-1)),t("div",j,[e[18]||(e[18]=t("label",null,"字体大小",-1)),n(_,{modelValue:l.fontSize,"onUpdate:modelValue":e[1]||(e[1]=a=>l.fontSize=a),min:12,max:24,step:1,onChange:x,style:{width:"200px"}},null,8,["modelValue"]),t("span",null,m(l.fontSize)+"px",1)]),t("div",q,[e[19]||(e[19]=t("label",null,"字体族",-1)),n(z,{modelValue:l.fontFamily,"onUpdate:modelValue":e[2]||(e[2]=a=>l.fontFamily=a),onChange:C},{default:s(()=>[n(v,{label:"系统默认",value:"system-ui"}),n(v,{label:"宋体",value:"SimSun"}),n(v,{label:"微软雅黑",value:"Microsoft YaHei"}),n(v,{label:"苹方",value:"PingFang SC"})]),_:1},8,["modelValue"])]),t("div",G,[e[20]||(e[20]=t("label",null,"行高",-1)),n(_,{modelValue:l.lineHeight,"onUpdate:modelValue":e[3]||(e[3]=a=>l.lineHeight=a),min:1.2,max:2,step:.1,onChange:U,style:{width:"200px"}},null,8,["modelValue"]),t("span",null,m(l.lineHeight),1)])])]),_:1}),n(f,{label:"阅读",name:"reading"},{default:s(()=>[t("div",J,[e[23]||(e[23]=t("h3",null,"页面设置",-1)),t("div",K,[e[22]||(e[22]=t("label",null,"页面宽度",-1)),n(_,{modelValue:l.pageWidth,"onUpdate:modelValue":e[4]||(e[4]=a=>l.pageWidth=a),min:600,max:1200,step:50,onChange:F,style:{width:"200px"}},null,8,["modelValue"]),t("span",null,m(l.pageWidth)+"px",1)])]),t("div",O,[e[26]||(e[26]=t("h3",null,"自动功能",-1)),t("div",Q,[n(S,{modelValue:l.autoSave,"onUpdate:modelValue":e[5]||(e[5]=a=>l.autoSave=a),onChange:h},null,8,["modelValue"]),e[24]||(e[24]=t("label",null,"自动保存阅读进度",-1))]),t("div",X,[n(S,{modelValue:l.autoBackup,"onUpdate:modelValue":e[6]||(e[6]=a=>l.autoBackup=a),onChange:k},null,8,["modelValue"]),e[25]||(e[25]=t("label",null,"自动备份数据",-1))])])]),_:1}),n(f,{label:"语言",name:"language"},{default:s(()=>[t("div",Z,[e[29]||(e[29]=t("h3",null,"界面语言",-1)),n(V,{modelValue:l.language,"onUpdate:modelValue":e[7]||(e[7]=a=>l.language=a),onChange:B},{default:s(()=>[n(i,{value:"zh-CN"},{default:s(()=>e[27]||(e[27]=[u("简体中文")])),_:1,__:[27]}),n(i,{value:"en-US"},{default:s(()=>e[28]||(e[28]=[u("English")])),_:1,__:[28]})]),_:1},8,["modelValue"])])]),_:1}),n(f,{label:"关于",name:"about"},{default:s(()=>[t("div",$,[e[31]||(e[31]=t("div",{class:"app-info"},[t("h2",null,"Yu Reader"),t("p",null,"版本: 1.0.0"),t("p",null,"一个现代化的电子书阅读器")],-1)),t("div",ee,[e[30]||(e[30]=t("h3",null,"系统信息",-1)),t("p",null,"平台: "+m(g.platform),1),t("p",null,"Electron: "+m(g.version),1)])])]),_:1})]),_:1},8,["modelValue"])])])}}}),se=M(te,[["__scopeId","data-v-076897ac"]]);export{se as default};
