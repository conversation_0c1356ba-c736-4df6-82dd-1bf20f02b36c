#!/usr/bin/env node

/**
 * 启动Yu Reader桌面应用
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动Yu Reader桌面应用...');
console.log('==========================================');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 检查必要文件
const mainFile = path.join(__dirname, 'dist', 'main', 'app.js');
const preloadFile = path.join(__dirname, 'dist', 'preload', 'index.js');
const packageFile = path.join(__dirname, 'package.json');

console.log('📁 文件检查:');
console.log('- 主进程文件:', fs.existsSync(mainFile) ? '✅' : '❌');
console.log('- 预加载文件:', fs.existsSync(preloadFile) ? '✅' : '❌');
console.log('- package.json:', fs.existsSync(packageFile) ? '✅' : '❌');

if (!fs.existsSync(mainFile)) {
    console.error('❌ 主进程文件不存在，请先运行: npm run build:main');
    process.exit(1);
}

if (!fs.existsSync(preloadFile)) {
    console.error('❌ 预加载文件不存在，请先运行: npm run build:preload');
    process.exit(1);
}

// 检查开发服务器
console.log('\n🌐 检查开发服务器...');
const http = require('http');

const checkServer = () => {
    return new Promise((resolve) => {
        const req = http.get('http://127.0.0.1:5173/', (res) => {
            resolve(true);
        });
        req.on('error', () => {
            resolve(false);
        });
        req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
        });
    });
};

checkServer().then(serverRunning => {
    console.log('- 开发服务器状态:', serverRunning ? '✅ 运行中' : '❌ 未运行');
    
    if (!serverRunning) {
        console.log('⚠️ 开发服务器未运行，请先运行: npm run dev:renderer');
        console.log('💡 或者在另一个终端中运行: npm run dev:renderer');
    }
    
    console.log('\n🚀 启动Electron应用...');
    
    // 尝试找到electron可执行文件
    let electronPath;
    const possiblePaths = [
        path.join(__dirname, 'node_modules', '.bin', 'electron.cmd'),
        path.join(__dirname, 'node_modules', '.bin', 'electron'),
        path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe'),
        'npx electron'
    ];
    
    for (const p of possiblePaths) {
        if (p === 'npx electron' || fs.existsSync(p)) {
            electronPath = p;
            break;
        }
    }
    
    if (!electronPath) {
        console.error('❌ 找不到Electron可执行文件');
        console.log('💡 请运行: npm install electron --save-dev');
        process.exit(1);
    }
    
    console.log('📱 使用Electron路径:', electronPath);
    
    // 启动Electron
    let child;
    if (electronPath === 'npx electron') {
        child = spawn('npx', ['electron', '.'], {
            stdio: 'inherit',
            cwd: __dirname,
            env: {
                ...process.env,
                NODE_ENV: 'development'
            },
            shell: true
        });
    } else {
        child = spawn(electronPath, ['.'], {
            stdio: 'inherit',
            cwd: __dirname,
            env: {
                ...process.env,
                NODE_ENV: 'development'
            },
            shell: true
        });
    }
    
    console.log('✅ Electron进程已启动，PID:', child.pid);
    console.log('🎉 桌面应用应该已经显示！');
    
    child.on('error', (error) => {
        console.error('❌ 启动失败:', error.message);
        console.log('💡 建议:');
        console.log('1. 检查Electron是否正确安装: npm list electron');
        console.log('2. 重新安装Electron: npm install electron --save-dev');
        console.log('3. 使用浏览器版本: http://127.0.0.1:5173/');
    });
    
    child.on('close', (code) => {
        console.log(`📱 应用退出，代码: ${code}`);
        if (code !== 0) {
            console.log('💡 如果应用没有正常显示，请尝试浏览器版本: http://127.0.0.1:5173/');
        }
    });
    
    // 处理进程退出
    process.on('SIGINT', () => {
        console.log('\n🛑 关闭应用...');
        child.kill('SIGINT');
        process.exit(0);
    });
    
    process.on('SIGTERM', () => {
        console.log('\n🛑 关闭应用...');
        child.kill('SIGTERM');
        process.exit(0);
    });
});
