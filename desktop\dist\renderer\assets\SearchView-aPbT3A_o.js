import{d as ee,b as f,q as M,v as R,e as le,c as p,f as l,a as s,w as a,r as i,l as u,u as T,R as te,s as B,S as se,T as oe,t as m,F as z,i as F,k as ae,A as ne,j as re,o as c,_ as ie}from"./index-krGsH_w3.js";import{u as ue}from"./bookshelf-BClHCnmU.js";const de={class:"search-view"},ce={class:"search-content"},pe={class:"search-box"},_e={class:"advanced-search"},ve={class:"search-filters"},fe={class:"filter-group"},me={class:"filter-group"},ge={class:"filter-group"},he={class:"filter-actions"},ke={class:"search-results"},Te={class:"results-header"},ye={class:"results-count"},Se={key:0,class:"books-grid"},Ve=["onClick"],we={class:"book-cover"},xe=["src","alt"],Be={class:"book-info"},Ce=["innerHTML"],be=["innerHTML"],Me={class:"book-meta"},Re={class:"file-type"},ze={class:"file-size"},Fe={key:0,class:"book-progress"},Le={key:1,class:"no-results"},qe={class:"no-results-icon"},Ue={key:2,class:"search-tips"},$e={class:"tips-icon"},He={class:"quick-searches"},Ne={class:"quick-tags"},Ae=ee({__name:"SearchView",setup(De){const L=re(),q=ue(),{books:U,searchBooks:$,setFilter:H}=q,d=f(""),y=f(!1),S=f("relevance"),n=f({fileTypes:[],readStatus:"all",sizeRange:[0,100]}),N=f(["小说","技术","历史","科幻","传记","JavaScript","Python","Vue","React"]),g=M(()=>{let t=U.value||[];if(d.value){const e=d.value.toLowerCase();t=t.filter(r=>r.title.toLowerCase().includes(e)||r.author.toLowerCase().includes(e))}return n.value.fileTypes&&n.value.fileTypes.length>0&&(t=t.filter(e=>n.value.fileTypes.includes(e.fileType))),n.value.readStatus!=="all"&&(t=t.filter(e=>{switch(n.value.readStatus){case"unread":return e.progress===0;case"reading":return e.progress>0&&e.progress<100;case"finished":return e.progress===100;default:return!0}})),t}),A=M(()=>{const t=[...g.value||[]];switch(S.value){case"title":return t.sort((e,r)=>e.title.localeCompare(r.title));case"author":return t.sort((e,r)=>e.author.localeCompare(r.author));case"addTime":return t.sort((e,r)=>r.addTime-e.addTime);case"lastRead":return t.sort((e,r)=>r.lastRead-e.lastRead);default:return t}}),V=()=>{$(d.value)},D=()=>{y.value=!y.value},E=()=>{n.value={fileTypes:[],readStatus:"all",sizeRange:[0,100]},w()},w=()=>{const t={};n.value.fileTypes&&n.value.fileTypes.length>0&&(t.formats=n.value.fileTypes),n.value.readStatus!=="all"&&(t.readingStatus=n.value.readStatus),H(t)},I=t=>{d.value=t,V()},P=t=>{L.push(`/reader/${t.id}`)};R(d,t=>{t.trim()&&V()},{debounce:300}),R(n,()=>{w()},{deep:!0});const C=t=>{if(!d.value)return t;const e=new RegExp(`(${d.value})`,"gi");return t.replace(e,"<mark>$1</mark>")},j=t=>{if(!t)return"0 B";const e=1024,r=["B","KB","MB","GB"],_=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,_)).toFixed(1))+" "+r[_]},G=t=>`${t} MB`;return le(()=>{console.log("搜索图书页面已加载")}),(t,e)=>{var b;const r=i("el-icon"),_=i("el-button"),J=i("el-input"),h=i("el-checkbox"),K=i("el-checkbox-group"),k=i("el-radio"),O=i("el-radio-group"),Q=i("el-slider"),X=i("el-collapse-transition"),v=i("el-option"),W=i("el-select"),Y=i("el-progress"),Z=i("el-tag");return c(),p("div",de,[e[24]||(e[24]=l("div",{class:"search-header"},[l("h1",{class:"page-title"},"搜索图书"),l("p",{class:"page-description"},"快速查找您的图书收藏")],-1)),l("div",ce,[l("div",pe,[s(J,{modelValue:d.value,"onUpdate:modelValue":e[0]||(e[0]=o=>d.value=o),placeholder:"输入书名、作者或关键词...",size:"large",clearable:"",onInput:V},{prefix:a(()=>[s(r,null,{default:a(()=>[s(T(B))]),_:1})]),append:a(()=>[s(_,{onClick:D},{default:a(()=>[s(r,null,{default:a(()=>[s(T(te))]),_:1}),e[5]||(e[5]=u(" 高级搜索 "))]),_:1,__:[5]})]),_:1},8,["modelValue"])]),s(X,null,{default:a(()=>[se(l("div",_e,[l("div",ve,[l("div",fe,[e[10]||(e[10]=l("label",null,"文件格式",-1)),s(K,{modelValue:n.value.fileTypes,"onUpdate:modelValue":e[1]||(e[1]=o=>n.value.fileTypes=o)},{default:a(()=>[s(h,{value:"epub"},{default:a(()=>e[6]||(e[6]=[u("EPUB")])),_:1,__:[6]}),s(h,{value:"pdf"},{default:a(()=>e[7]||(e[7]=[u("PDF")])),_:1,__:[7]}),s(h,{value:"mobi"},{default:a(()=>e[8]||(e[8]=[u("MOBI")])),_:1,__:[8]}),s(h,{value:"txt"},{default:a(()=>e[9]||(e[9]=[u("TXT")])),_:1,__:[9]})]),_:1},8,["modelValue"])]),l("div",me,[e[15]||(e[15]=l("label",null,"阅读状态",-1)),s(O,{modelValue:n.value.readStatus,"onUpdate:modelValue":e[2]||(e[2]=o=>n.value.readStatus=o)},{default:a(()=>[s(k,{value:"all"},{default:a(()=>e[11]||(e[11]=[u("全部")])),_:1,__:[11]}),s(k,{value:"unread"},{default:a(()=>e[12]||(e[12]=[u("未读")])),_:1,__:[12]}),s(k,{value:"reading"},{default:a(()=>e[13]||(e[13]=[u("在读")])),_:1,__:[13]}),s(k,{value:"finished"},{default:a(()=>e[14]||(e[14]=[u("已读")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),l("div",ge,[e[16]||(e[16]=l("label",null,"文件大小",-1)),s(Q,{modelValue:n.value.sizeRange,"onUpdate:modelValue":e[3]||(e[3]=o=>n.value.sizeRange=o),range:"",min:0,max:100,"format-tooltip":G},null,8,["modelValue"])]),l("div",he,[s(_,{onClick:E},{default:a(()=>e[17]||(e[17]=[u("重置")])),_:1,__:[17]}),s(_,{type:"primary",onClick:w},{default:a(()=>e[18]||(e[18]=[u("应用筛选")])),_:1,__:[18]})])])],512),[[oe,y.value]])]),_:1}),l("div",ke,[l("div",Te,[l("span",ye," 找到 "+m(((b=g.value)==null?void 0:b.length)||0)+" 本图书 ",1),s(W,{modelValue:S.value,"onUpdate:modelValue":e[4]||(e[4]=o=>S.value=o),placeholder:"排序方式",style:{width:"150px"}},{default:a(()=>[s(v,{label:"相关性",value:"relevance"}),s(v,{label:"书名",value:"title"}),s(v,{label:"作者",value:"author"}),s(v,{label:"添加时间",value:"addTime"}),s(v,{label:"最后阅读",value:"lastRead"})]),_:1},8,["modelValue"])]),g.value&&g.value.length>0?(c(),p("div",Se,[(c(!0),p(z,null,F(A.value,o=>{var x;return c(),p("div",{key:o.id,class:"book-card",onClick:Ee=>P(o)},[l("div",we,[l("img",{src:o.cover||"/placeholder-book.png",alt:o.title},null,8,xe)]),l("div",Be,[l("h3",{innerHTML:C(o.title)},null,8,Ce),l("p",{innerHTML:C(o.author)},null,8,be),l("div",Me,[l("span",Re,m((x=o.fileType)==null?void 0:x.toUpperCase()),1),l("span",ze,m(j(o.fileSize)),1)]),o.progress>0?(c(),p("div",Fe,[s(Y,{percentage:o.progress,"show-text":!1},null,8,["percentage"]),l("span",null,m(o.progress)+"%",1)])):ae("",!0)])],8,Ve)}),128))])):d.value?(c(),p("div",Le,[l("div",qe,[s(r,null,{default:a(()=>[s(T(B))]),_:1})]),e[19]||(e[19]=l("h3",null,"未找到相关图书",-1)),e[20]||(e[20]=l("p",null,"尝试使用不同的关键词或调整筛选条件",-1))])):(c(),p("div",Ue,[l("div",$e,[s(r,null,{default:a(()=>[s(T(B))]),_:1})]),e[22]||(e[22]=l("h3",null,"开始搜索",-1)),e[23]||(e[23]=l("p",null,"输入书名、作者或关键词来查找图书",-1)),l("div",He,[e[21]||(e[21]=l("h4",null,"快速搜索",-1)),l("div",Ne,[(c(!0),p(z,null,F(N.value||[],o=>(c(),ne(Z,{key:o,onClick:x=>I(o),class:"quick-tag"},{default:a(()=>[u(m(o),1)]),_:2},1032,["onClick"]))),128))])])]))])])])}}}),je=ie(Ae,[["__scopeId","data-v-e8df71a1"]]);export{je as default};
