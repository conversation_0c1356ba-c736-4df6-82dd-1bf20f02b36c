/**
 * 简化的Electron启动脚本
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动Yu Reader桌面应用...');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 检查必要文件
const mainFile = path.join(__dirname, 'dist', 'main', 'app.js');
const electronExe = path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe');

console.log('检查文件:');
console.log('- 主进程文件:', fs.existsSync(mainFile) ? '✅' : '❌');
console.log('- Electron可执行文件:', fs.existsSync(electronExe) ? '✅' : '❌');

if (!fs.existsSync(mainFile)) {
    console.error('❌ 主进程文件不存在，请先运行开发服务器');
    process.exit(1);
}

if (!fs.existsSync(electronExe)) {
    console.error('❌ Electron未安装，请等待安装完成');
    process.exit(1);
}

console.log('✅ 启动Electron应用...');

// 启动Electron
const child = spawn(electronExe, ['.'], {
    stdio: 'inherit',
    cwd: __dirname,
    env: {
        ...process.env,
        NODE_ENV: 'development'
    }
});

child.on('error', (error) => {
    console.error('❌ 启动失败:', error.message);
});

child.on('close', (code) => {
    console.log(`📱 应用退出，代码: ${code}`);
});

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n🛑 关闭应用...');
    child.kill('SIGINT');
    process.exit(0);
});

console.log('✅ Electron应用已启动，PID:', child.pid);
