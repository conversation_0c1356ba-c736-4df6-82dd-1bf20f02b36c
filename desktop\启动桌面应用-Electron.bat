@echo off
chcp 65001 >nul
title Yu Reader Electron 桌面应用启动器

echo.
echo ========================================
echo    🚀 Yu Reader Electron 桌面应用启动器
echo ========================================
echo.

REM 设置环境变量
set NODE_ENV=development
set ELECTRON_IS_DEV=1

echo 🔍 当前目录: %CD%
echo 🔍 环境变量: NODE_ENV=%NODE_ENV%
echo.

REM 检查Node.js和npm环境
echo 📋 检查运行环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或不在 PATH 中
    echo 💡 请安装 Node.js 18+ 版本
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装或不在 PATH 中
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% 和 npm %NPM_VERSION% 环境正常

REM 检查依赖
echo.
echo 📦 检查项目依赖...
if not exist "node_modules" (
    echo ⚠️ 依赖未安装，正在安装...
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        echo 💡 请检查网络连接或尝试使用淘宝镜像：
        echo    npm install --registry=https://registry.npmmirror.com
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

REM 检查Electron是否安装
echo.
echo 🔍 检查 Electron...
if not exist "node_modules\electron" (
    echo ❌ Electron 未安装
    echo 💡 正在安装 Electron...
    call npm install electron --save-dev
    if errorlevel 1 (
        echo ❌ Electron 安装失败
        pause
        exit /b 1
    )
)
echo ✅ Electron 已安装

REM 检查必要文件并构建
echo.
echo 🔨 检查构建文件...
set NEED_BUILD=0

if not exist "dist\main\app.js" (
    echo ⚠️ 主进程文件不存在
    set NEED_BUILD=1
)

if not exist "dist\preload\index.js" (
    echo ⚠️ 预加载文件不存在
    set NEED_BUILD=1
)

if not exist "dist\renderer\index.html" (
    echo ⚠️ 渲染进程文件不存在
    set NEED_BUILD=1
)

if %NEED_BUILD%==1 (
    echo 🔨 正在构建项目...
    echo 💡 这可能需要几分钟时间，请耐心等待...
    call npm run build
    if errorlevel 1 (
        echo ❌ 项目构建失败
        echo 💡 请检查代码是否有语法错误
        echo 💡 可以尝试运行: npm run build:main 和 npm run build:renderer 分别构建
        pause
        exit /b 1
    )
    echo ✅ 项目构建完成
) else (
    echo ✅ 构建文件完整
)

REM 启动应用
echo.
echo 🚀 启动 Yu Reader Electron 桌面应用...
echo 💡 应用启动后，此窗口将保持打开以显示日志信息
echo 💡 如果应用窗口没有出现，请检查任务栏或等待几秒钟
echo.

call npm start

if errorlevel 1 (
    echo.
    echo ❌ 应用启动失败
    echo 💡 可能的解决方案：
    echo    1. 检查端口 5173 是否被占用
    echo    2. 重新构建项目：npm run build
    echo    3. 清理缓存：删除 node_modules 文件夹后重新 npm install
    echo    4. 检查防火墙是否阻止了 Electron
    echo.
    echo 🔧 尝试诊断模式启动...
    echo 正在运行: npx electron . --verbose
    npx electron . --verbose
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Yu Reader Electron 应用已退出
echo 💡 如果需要重新启动，请再次运行此脚本
pause
