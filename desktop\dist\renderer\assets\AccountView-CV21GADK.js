import{d as t,e as c,c as a,f as e,a as n,r as p,o as _,_ as d}from"./index-krGsH_w3.js";const r={class:"account-view"},i={class:"account-content"},l=t({__name:"AccountView",setup(m){return c(()=>{console.log("账号设置页面已加载")}),(u,o)=>{const s=p("el-empty");return _(),a("div",r,[o[0]||(o[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"账号设置"),e("p",{class:"page-description"},"管理账号信息")],-1)),e("div",i,[n(s,{description:"账号设置功能开发中..."})])])}}}),v=d(l,[["__scopeId","data-v-832327d3"]]);export{v as default};
