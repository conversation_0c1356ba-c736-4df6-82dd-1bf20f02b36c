/**
 * Electron桌面应用启动器
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动Electron桌面应用...');
console.log('==========================================');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 检查文件
const electronExe = path.resolve(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe');
const mainFile = path.resolve(__dirname, 'dist', 'main', 'app.js');
const packageFile = path.resolve(__dirname, 'package.json');

console.log('📁 文件检查:');
console.log('- 工作目录:', __dirname);
console.log('- Electron路径:', electronExe);
console.log('- Electron存在:', fs.existsSync(electronExe) ? '✅' : '❌');
console.log('- 主进程文件:', fs.existsSync(mainFile) ? '✅' : '❌');
console.log('- package.json:', fs.existsSync(packageFile) ? '✅' : '❌');

if (!fs.existsSync(electronExe)) {
    console.error('❌ Electron可执行文件不存在');
    process.exit(1);
}

if (!fs.existsSync(mainFile)) {
    console.error('❌ 主进程文件不存在');
    process.exit(1);
}

// 检查package.json配置
try {
    const pkg = JSON.parse(fs.readFileSync(packageFile, 'utf8'));
    console.log('- 应用名称:', pkg.name);
    console.log('- main字段:', pkg.main);
} catch (error) {
    console.error('❌ package.json读取失败:', error.message);
}

// 检查开发服务器
console.log('\n🌐 检查开发服务器...');
const http = require('http');

const checkServer = () => {
    return new Promise((resolve) => {
        const req = http.get('http://127.0.0.1:5173/', (res) => {
            resolve(true);
        });
        req.on('error', () => {
            resolve(false);
        });
        req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
        });
    });
};

checkServer().then(serverRunning => {
    console.log('- 开发服务器状态:', serverRunning ? '✅ 运行中' : '❌ 未运行');
    
    if (!serverRunning) {
        console.log('⚠️ 开发服务器未运行，Electron应用可能无法正常显示');
    }
    
    console.log('\n🚀 启动Electron应用...');
    
    // 使用spawn启动
    const args = ['.'];
    console.log('执行命令:', electronExe, args.join(' '));
    
    const child = spawn(electronExe, args, {
        stdio: ['ignore', 'pipe', 'pipe'],
        cwd: __dirname,
        env: {
            ...process.env,
            NODE_ENV: 'development',
            ELECTRON_ENABLE_LOGGING: '1',
            ELECTRON_ENABLE_STACK_DUMPING: '1'
        },
        detached: false,
        windowsHide: false
    });
    
    console.log('📱 Electron进程已启动，PID:', child.pid);
    
    let hasOutput = false;
    
    child.stdout.on('data', (data) => {
        hasOutput = true;
        console.log('📤 stdout:', data.toString().trim());
    });
    
    child.stderr.on('data', (data) => {
        hasOutput = true;
        console.log('📤 stderr:', data.toString().trim());
    });
    
    child.on('error', (error) => {
        console.error('❌ 进程启动错误:', error.message);
        console.log('💡 可能的解决方案:');
        console.log('1. 检查Electron文件是否损坏');
        console.log('2. 尝试重新安装Electron');
        console.log('3. 检查系统权限设置');
    });
    
    child.on('spawn', () => {
        console.log('✅ Electron进程已成功启动');
    });
    
    child.on('close', (code, signal) => {
        console.log(`📱 Electron进程退出 - 代码: ${code}, 信号: ${signal}`);
        if (code !== 0) {
            console.log('❌ 应用异常退出');
        }
    });
    
    child.on('exit', (code, signal) => {
        console.log(`📱 Electron进程结束 - 代码: ${code}, 信号: ${signal}`);
    });
    
    // 10秒后检查状态
    setTimeout(() => {
        if (child.exitCode === null && !child.killed) {
            console.log('✅ Electron应用正在运行！');
            console.log('🎉 如果看到这条消息，桌面应用应该已经显示');
            console.log('💡 如果没有看到窗口，请检查任务栏或最小化的窗口');
        } else {
            console.log('❌ Electron应用已退出或启动失败');
            console.log('💡 建议使用浏览器版本: http://127.0.0.1:5173/');
        }
    }, 10000);
    
    // 处理进程退出
    process.on('SIGINT', () => {
        console.log('\n🛑 收到退出信号，关闭Electron...');
        if (!child.killed) {
            child.kill('SIGTERM');
        }
        setTimeout(() => {
            if (!child.killed) {
                child.kill('SIGKILL');
            }
            process.exit(0);
        }, 2000);
    });
    
    // 保持Node.js进程运行
    console.log('⏳ 保持启动器运行，按Ctrl+C退出...');
});
