@echo off
chcp 65001 >nul
title Yu Reader 启动器

echo 🚀 启动 Yu Reader 桌面应用...
echo.

REM 切换到正确的目录
cd /d "%~dp0"

REM 检查 Node.js 和 npm
echo 检查环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或不在 PATH 中
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装或不在 PATH 中
    pause
    exit /b 1
)

echo ✅ Node.js 和 npm 环境正常

REM 检查依赖
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 构建项目
echo 🔨 构建项目...
npm run build
if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

REM 启动应用
echo 🚀 启动应用...
npm start

pause
