@echo off
chcp 65001 >nul
title Yu Reader Electron 启动器

echo.
echo 🚀 Yu Reader Electron 快速启动器
echo.

REM 切换到正确的目录
cd /d "%~dp0"

REM 检查环境
echo 📋 检查环境...
node --version >nul 2>&1 || (
    echo ❌ 需要安装 Node.js
    pause & exit /b 1
)

npm --version >nul 2>&1 || (
    echo ❌ 需要安装 npm
    pause & exit /b 1
)

echo ✅ 环境检查通过

REM 检查依赖
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install || (
        echo ❌ 依赖安装失败
        pause & exit /b 1
    )
)

REM 检查构建文件
if not exist "dist\main\app.js" (
    echo 🔨 构建主进程...
    npm run build:main || (
        echo ❌ 主进程构建失败
        pause & exit /b 1
    )
)

if not exist "dist\preload\index.js" (
    echo 🔨 构建预加载脚本...
    npm run build:preload || (
        echo ❌ 预加载脚本构建失败
        pause & exit /b 1
    )
)

if not exist "dist\renderer\index.html" (
    echo 🔨 构建渲染进程...
    npm run build:renderer || (
        echo ❌ 渲染进程构建失败
        pause & exit /b 1
    )
)

echo ✅ 构建文件完整

REM 启动应用
echo 🚀 启动 Electron 应用...
npm start

echo.
echo ✅ 应用已退出
pause
