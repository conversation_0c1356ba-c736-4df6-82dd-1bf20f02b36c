import{d as t,e as a,c as n,f as e,a as c,r,o as p,_}from"./index-krGsH_w3.js";const d={class:"bookmarks-view"},i={class:"bookmarks-content"},l=t({__name:"BookmarksView",setup(m){return a(()=>{console.log("书签管理页面已加载")}),(k,o)=>{const s=r("el-empty");return p(),n("div",d,[o[0]||(o[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"书签管理"),e("p",{class:"page-description"},"查看和管理所有书签")],-1)),e("div",i,[c(s,{description:"书签管理功能开发中..."})])])}}}),v=_(l,[["__scopeId","data-v-3842cdf8"]]);export{v as default};
