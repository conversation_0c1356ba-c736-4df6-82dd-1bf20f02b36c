<!--
  PDF阅读器视图组件

  技术架构：
  - 使用vue3-pdf-app作为核心PDF渲染引擎，替代原有的多个PDF库依赖
  - vue3-pdf-app基于PDF.js，提供完整的PDF查看器功能
  - 支持自定义工具栏插槽，实现个性化的用户界面
  - 集成Element Plus UI组件，提供一致的用户体验

  主要功能：
  - PDF文档加载和渲染（支持ArrayBuffer和URL两种方式）
  - 页面导航（上一页、下一页、跳转到指定页面）
  - 缩放控制（放大、缩小、适应宽度、适应页面、实际大小）
  - 页面旋转（90度递增旋转）
  - 文本搜索和高亮显示
  - 主题切换（明亮/暗黑模式）
  - 打印和下载功能
  - 密码保护PDF文档处理
  - 大文件优化（文件大小检测、加载进度显示）
  - 书签管理和阅读进度保存

  性能优化：
  - 懒加载机制，避免一次性加载整个文档
  - 加载进度指示器，提升大文件加载体验
  - 防抖搜索，减少大文档搜索时的性能影响
  - 文件大小检测和警告机制

  重构说明：
  - 从@tato30/vue-pdf、pdfjs-dist、vue-pdf-embed等多个依赖统一为vue3-pdf-app
  - 移除了iframe回退方案，统一使用vue3-pdf-app处理所有场景
  - 简化了PDF加载逻辑，提高了代码可维护性
  - 增强了错误处理和用户反馈机制
-->

<template>
  <div class="pdf-reader-view">
    <!-- 工具栏 -->
    <div class="toolbar">
      <!-- 返回按钮 -->
      <div class="toolbar-left">
        <el-button @click="goBack" type="primary" :icon="ArrowLeft">
          返回图书列表
        </el-button>
      </div>

      <!-- 书籍信息 -->
      <div class="book-info" v-if="currentBook">
        <span class="book-title">{{ currentBook.title }}</span>
        <span class="reading-progress">{{ progressText }}</span>
      </div>

      <!-- 页面控制 -->
      <div class="toolbar-center">
        <div class="page-controls">
          <el-button
            @click="previousPage"
            :disabled="currentPage <= 1"
            :icon="ArrowLeft"
            size="small"
            title="上一页"
          />

          <span class="page-info">
            <el-input-number
              v-model="currentPage"
              :min="1"
              :max="totalPages"
              size="small"
              controls-position="right"
              @change="goToPage"
              style="width: 80px;"
            />
            <span class="page-separator"> / </span>
            <span class="total-pages">{{ totalPages }}</span>
          </span>

          <el-button
            @click="nextPage"
            :disabled="currentPage >= totalPages"
            :icon="ArrowRight"
            size="small"
            title="下一页"
          />
        </div>
      </div>

      <!-- 工具按钮 -->
      <div class="toolbar-right">
        <!-- 缩放控制 -->
        <div class="zoom-controls">
          <el-button @click="zoomOut" :icon="ZoomOut" size="small" title="缩小" />
          <span class="zoom-level">{{ Math.round(scale * 100) }}%</span>
          <el-button @click="zoomIn" :icon="ZoomIn" size="small" title="放大" />
        </div>

        <!-- 其他工具 -->
        <div class="tool-controls">
          <el-button @click="rotatePage" :icon="RefreshRight" size="small" title="旋转页面" />
          <el-button @click="toggleSidebar" :icon="Menu" size="small" title="侧边栏" />
          <el-button @click="toggleSearch" :icon="Search" size="small" title="搜索" />
          <el-dropdown @command="handleToolCommand">
            <el-button :icon="MoreFilled" size="small" title="更多工具" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="print" :icon="Printer">打印</el-dropdown-item>
                <el-dropdown-item command="download" :icon="Download">下载</el-dropdown-item>
                <el-dropdown-item command="fitWidth" :icon="FullScreen">适应宽度</el-dropdown-item>
                <el-dropdown-item command="fitPage" :icon="ScaleToOriginal">适应页面</el-dropdown-item>
                <el-dropdown-item command="actualSize" :icon="Crop">实际大小</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>


    <!-- 搜索栏 -->
    <div v-if="showSearch" class="search-bar">
      <div class="search-container">
        <el-input
          v-model="searchQuery"
          placeholder="搜索PDF内容..."
          size="small"
          @input="onSearchInput"
          @keyup.enter="searchNext"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <div class="search-controls">
          <el-button @click="searchPrevious" size="small" :disabled="!searchResults.length">上一个</el-button>
          <el-button @click="searchNext" size="small" :disabled="!searchResults.length">下一个</el-button>
          <span class="search-results" v-if="searchResults.length">
            {{ currentSearchIndex + 1 }} / {{ searchResults.length }}
          </span>
          <el-button @click="toggleSearch" :icon="Close" size="small" circle />
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content-area">
      <!-- 侧边栏 -->
      <div v-if="showSidebar" class="sidebar">
        <!-- 侧边栏标签页 -->
        <div class="sidebar-tabs">
          <el-button
            @click="sidebarMode = 'thumbnails'"
            :type="sidebarMode === 'thumbnails' ? 'primary' : 'default'"
            size="small"
          >
            缩略图
          </el-button>
          <el-button
            @click="sidebarMode = 'bookmarks'"
            :type="sidebarMode === 'bookmarks' ? 'primary' : 'default'"
            size="small"
          >
            书签
          </el-button>
        </div>

        <!-- 缩略图面板 -->
        <div v-if="sidebarMode === 'thumbnails'" class="thumbnails-panel">
          <div class="thumbnail-list">
            <div
              v-for="pageNum in totalPages"
              :key="pageNum"
              class="thumbnail-item"
              :class="{ active: pageNum === currentPage }"
              @click="goToPage(pageNum)"
            >
              <div class="thumbnail-page">{{ pageNum }}</div>
            </div>
          </div>
        </div>

        <!-- 书签面板 -->
        <div v-else class="bookmarks-panel">
          <div class="bookmark-list">
            <div
              v-for="bookmark in bookmarks"
              :key="bookmark.id"
              class="bookmark-item"
            >
              <div class="bookmark-content" @click="goToBookmark(bookmark)">
                <div class="bookmark-title">{{ bookmark.title }}</div>
                <div class="bookmark-page">第 {{ bookmark.pageNumber }} 页</div>
                <div class="bookmark-date">{{ formatDate(bookmark.createdAt) }}</div>
              </div>
              <div class="bookmark-actions-inline">
                <el-button
                  @click.stop="removeBookmark(bookmark)"
                  :icon="Delete"
                  size="small"
                  type="danger"
                  text
                  title="删除书签"
                />
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="bookmarks.length === 0" class="empty-bookmarks">
              <el-icon><BookmarkOne /></el-icon>
              <p>暂无书签</p>
            </div>
          </div>

          <!-- 添加书签按钮 -->
          <div class="bookmark-actions">
            <el-button @click="addBookmark" type="primary" size="small" style="width: 100%">
              添加书签
            </el-button>
          </div>
        </div>
      </div>

      <!-- PDF显示区域 -->
      <div class="pdf-viewer" ref="viewerContainer">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <div class="loading-content">
            <span>{{ loadingMessage }}</span>
            <div v-if="loadingProgress > 0" class="loading-progress">
              <el-progress :percentage="loadingProgress" :show-text="false" />
              <span class="progress-text">{{ loadingProgress }}%</span>
            </div>
            <div v-if="fileSize > 0" class="file-info">
              文件大小: {{ formatFileSize(fileSize) }}
            </div>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <el-icon class="error-icon">
            <WarningFilled />
          </el-icon>
          <div class="error-content">
            <h3 class="error-title">{{ errorTitle }}</h3>
            <p class="error-message">{{ error }}</p>
            <div class="error-details" v-if="errorDetails">
              <el-collapse>
                <el-collapse-item title="错误详情" name="details">
                  <pre class="error-stack">{{ errorDetails }}</pre>
                </el-collapse-item>
              </el-collapse>
            </div>
            <div class="error-actions">
              <el-button @click="retryLoadPdf" type="primary" :loading="isRetrying">
                重试加载
              </el-button>
              <el-button @click="selectDifferentFile" type="default">
                选择其他文件
              </el-button>
              <el-button @click="reportError" type="text">
                报告问题
              </el-button>
            </div>
            <div class="error-suggestions" v-if="errorSuggestions.length > 0">
              <h4>解决建议：</h4>
              <ul>
                <li v-for="suggestion in errorSuggestions" :key="suggestion">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- PDF组件 - 使用vue3-pdf-app统一处理 -->
        <div v-else-if="pdfSource" class="pdf-container">
          <VuePdfApp
            :pdf="pdfSource"
            :theme="pdfTheme"
            :page-scale="scale"
            :page-number="currentPage"
            :config="pdfConfig"
            @after-created="onPdfAppCreated"
            @open="onPdfOpen"
            @pages-rendered="onPagesRendered"
            class="vue-pdf-app-component"
          >
            <!-- 自定义工具栏插槽 -->
            <template #toolbar-left-prepend>
              <div class="custom-toolbar-left">
                <!-- 页面导航控件 -->
                <el-button-group>
                  <el-button @click="previousPage" :disabled="currentPage <= 1" :icon="ArrowLeft" size="small">
                    上一页
                  </el-button>
                  <el-button @click="nextPage" :disabled="currentPage >= totalPages" :icon="ArrowRight" size="small">
                    下一页
                  </el-button>
                </el-button-group>

                <!-- 页面跳转 -->
                <div class="page-navigation">
                  <el-input
                    v-model="pageInput"
                    @keyup.enter="goToPageFromInput"
                    @blur="goToPageFromInput"
                    size="small"
                    style="width: 60px;"
                  />
                  <span class="page-separator">/</span>
                  <span class="total-pages">{{ totalPages }}</span>
                </div>
              </div>
            </template>

            <!-- 自定义右侧工具栏 -->
            <template #toolbar-right-append>
              <div class="custom-toolbar-right">
                <!-- 缩放控件 -->
                <el-button-group>
                  <el-button @click="zoomOut" :icon="ZoomOut" size="small" title="缩小">
                  </el-button>
                  <el-button @click="resetZoom" size="small" title="重置缩放">
                    {{ Math.round(scale * 100) }}%
                  </el-button>
                  <el-button @click="zoomIn" :icon="ZoomIn" size="small" title="放大">
                  </el-button>
                </el-button-group>

                <!-- 旋转控件 -->
                <el-button @click="rotatePage" :icon="RefreshRight" size="small" title="旋转">
                </el-button>

                <!-- 搜索控件 -->
                <el-button @click="toggleSearch" :icon="Search" size="small" title="搜索" :type="showSearch ? 'primary' : 'default'">
                </el-button>

                <!-- 适应控件 -->
                <el-dropdown @command="handleFitCommand">
                  <el-button size="small" :icon="ScaleToOriginal">
                    适应<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="width">适应宽度</el-dropdown-item>
                      <el-dropdown-item command="page">适应页面</el-dropdown-item>
                      <el-dropdown-item command="actual">实际大小</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

                <!-- 主题切换 -->
                <el-button @click="toggleTheme" size="small" title="切换主题">
                  {{ pdfTheme === 'light' ? '🌙' : '☀️' }}
                </el-button>

                <!-- 性能监控（仅在启用性能优化时显示） -->
                <el-dropdown v-if="virtualScrollConfig.enabled" trigger="hover">
                  <el-button size="small" type="text" title="性能监控">
                    📊
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <div class="performance-monitor">
                        <div class="monitor-item">
                          <span>内存使用:</span>
                          <span :class="getMemoryStatusClass()">{{ performanceMetrics.memoryUsage.toFixed(1) }}MB</span>
                        </div>
                        <div class="monitor-item">
                          <span>缓存命中率:</span>
                          <span>{{ performanceMetrics.cacheHitRate.toFixed(1) }}%</span>
                        </div>
                        <div class="monitor-item">
                          <span>缓存页面:</span>
                          <span>{{ pageRenderCache.size }}/{{ virtualScrollConfig.maxCacheSize }}</span>
                        </div>
                        <div class="monitor-item" v-if="memoryLeakDetection.enabled">
                          <span>内存监控:</span>
                          <span class="status-indicator" :class="memoryLeakDetection.consecutiveIncreases > 0 ? 'warning' : 'normal'">
                            {{ memoryLeakDetection.consecutiveIncreases > 0 ? '⚠️' : '✅' }}
                          </span>
                        </div>
                        <div class="monitor-item" v-if="memoryHistory.length > 1">
                          <span>内存趋势:</span>
                          <span :class="getMemoryTrendClass()">{{ getMemoryTrend() }}</span>
                        </div>
                        <el-divider style="margin: 8px 0;" />
                        <div class="monitor-actions">
                          <el-button @click="triggerMemoryCleanup" size="small" type="primary" style="width: 48%;">
                            清理内存
                          </el-button>
                          <el-button @click="toggleMemoryMonitoring" size="small" style="width: 48%;">
                            {{ memoryMonitorConfig.enabled ? '停止监控' : '开始监控' }}
                          </el-button>
                        </div>
                      </div>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>

            <!-- 搜索栏插槽 -->
            <template #viewer-header v-if="showSearch">
              <div class="search-bar">
                <div class="search-container">
                  <el-input
                    v-model="searchQuery"
                    placeholder="搜索PDF内容..."
                    @keyup.enter="performSearch"
                    @input="onSearchInput"
                    size="small"
                    clearable
                  >
                    <template #prefix>
                      <el-icon><Search /></el-icon>
                    </template>
                  </el-input>

                  <el-button-group class="search-controls">
                    <el-button @click="searchPrevious" :disabled="searchResults.length === 0" size="small">
                      上一个
                    </el-button>
                    <el-button @click="searchNext" :disabled="searchResults.length === 0" size="small">
                      下一个
                    </el-button>
                  </el-button-group>

                  <span class="search-results-info" v-if="searchResults.length > 0">
                    {{ currentSearchIndex + 1 }} / {{ searchResults.length }}
                  </span>

                  <el-button @click="toggleSearch" :icon="Close" size="small" type="text">
                  </el-button>
                </div>
              </div>
            </template>
          </VuePdfApp>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  ArrowDown,
  ZoomIn,
  ZoomOut,
  Loading,
  WarningFilled,
  Menu,
  Search,
  Close,
  RefreshRight,
  MoreFilled,
  Printer,
  Download,
  FullScreen,
  ScaleToOriginal,
  Crop,
  Delete,
  BookmarkOne
} from '@element-plus/icons-vue'
import { useReaderStore } from '../../store/reader'

// 导入vue3-pdf-app组件
// vue3-pdf-app是一个基于PDF.js的Vue 3 PDF查看器组件
// 提供完整的PDF查看功能，包括工具栏、搜索、缩放等
import VuePdfApp from 'vue3-pdf-app'
// 导入vue3-pdf-app的图标样式，用于工具栏按钮显示
import 'vue3-pdf-app/dist/icons/main.css'

// 导入PDF设置管理器和书签管理器
import { PdfSettingsManager } from '../../services/PdfSettingsManager'
import { PdfBookmarkManager } from '../../services/PdfBookmarkManager'



// Props
interface Props {
  /** 书籍ID */
  bookId: string
  /** 书籍信息（可选，用于兼容性） */
  bookInfo?: any
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'go-back'): void
  (e: 'select-file'): void
}

const emit = defineEmits<Emits>()

// 路由
const router = useRouter()

// 响应式数据
const isLoading = ref(true)
const error = ref<string>('')
const errorTitle = ref<string>('') // 错误标题
const errorDetails = ref<string>('') // 错误详细信息
const errorSuggestions = ref<string[]>([]) // 错误解决建议
const isRetrying = ref(false) // 重试状态
const loadingMessage = ref('正在加载PDF文档...')
const loadingProgress = ref(0) // 加载进度 0-100
const fileSize = ref(0) // 文件大小（字节）
const currentPage = ref(1)
const totalPages = ref(0)
const pageInput = ref('1') // 页面输入框的值
const scale = ref(1.0)
const rotation = ref(0)
const fitParent = ref(false)
const startTime = ref(0) // 页面切换开始时间

// ==================== 大文件性能优化相关状态 ====================

// 虚拟滚动和缓存配置
const virtualScrollConfig = ref({
  enabled: false, // 是否启用虚拟滚动
  pageBufferSize: 3, // 缓存页面数量（当前页前后各缓存几页）
  renderThreshold: 50, // 启用虚拟滚动的页面数阈值
  maxCacheSize: 10 // 最大缓存页面数
})

// 页面渲染缓存
const pageRenderCache = ref(new Map<number, any>()) // 页面渲染缓存
const visiblePages = ref(new Set<number>()) // 当前可见的页面
const renderQueue = ref<number[]>([]) // 待渲染页面队列
const isRenderingPage = ref(false) // 是否正在渲染页面

// 性能监控
const performanceMetrics = ref({
  memoryUsage: 0, // 内存使用量（MB）
  renderTime: 0, // 页面渲染时间（ms）
  cacheHitRate: 0, // 缓存命中率
  lastUpdateTime: Date.now()
})

// ==================== 内存管理优化 ====================

// 内存监控配置
const memoryMonitorConfig = ref({
  enabled: false, // 是否启用内存监控
  warningThreshold: 100, // 内存警告阈值（MB）
  criticalThreshold: 200, // 内存严重警告阈值（MB）
  checkInterval: 5000, // 检查间隔（ms）
  maxMemoryHistory: 20 // 最大内存历史记录数
})

// 内存使用历史
const memoryHistory = ref<Array<{
  timestamp: number
  usage: number
  cacheSize: number
}>>([])

// 内存监控定时器
const memoryMonitorTimer = ref<NodeJS.Timeout | null>(null)

// 内存泄漏检测
const memoryLeakDetection = ref({
  enabled: false,
  baselineMemory: 0, // 基准内存使用量
  consecutiveIncreases: 0, // 连续增长次数
  leakThreshold: 5, // 连续增长阈值
  lastCleanupTime: Date.now()
})

const showSidebar = ref(false)
const sidebarMode = ref<'bookmarks' | 'thumbnails'>('bookmarks')
const bookmarks = ref<any[]>([])

// 搜索相关
const showSearch = ref(false)
const searchQuery = ref('')
const searchResults = ref<any[]>([])
const currentSearchIndex = ref(0)
const searchInputTimer = ref<NodeJS.Timeout | null>(null)

// 高亮选项
const highlightOptions = ref({
  completeWords: false,
  ignoreCase: true
})

// PDF设置管理器
const pdfSettings = new PdfSettingsManager()
const pdfConfig = ref(pdfSettings.getConfig())

// PDF书签管理器
const bookmarkManager = new PdfBookmarkManager()

// 监听设置变化
pdfSettings.addListener((newConfig) => {
  pdfConfig.value = newConfig
  // 应用新的缩放设置
  if (scale.value !== newConfig.defaultZoom) {
    scale.value = newConfig.defaultZoom
  }
})

// ==================== PDF核心状态管理 ====================

// PDF文件路径，用于监听文件变化并触发加载
const pdfFilePath = ref('')
// PDF数据源，支持ArrayBuffer（本地文件）和string（URL）两种格式
// vue3-pdf-app会根据数据类型自动选择合适的加载方式
const pdfSource = ref<string | ArrayBuffer | null>(null)
// vue3-pdf-app组件实例的引用，用于调用其内部API（如搜索、缩放等）
const pdfApp = ref<any>(null)
// 检测是否在Electron环境中运行
const isElectron = ref(!!window.electronAPI)

// ==================== PDF主题和配置 ====================

// PDF查看器主题配置，支持明亮和暗黑两种模式
const pdfTheme = ref<'light' | 'dark'>('light')



// 监听PDF信息变化，获取总页数
watch(info, (newInfo) => {
  if (newInfo && newInfo.numPages) {
    totalPages.value = newInfo.numPages
    console.log('PDF总页数:', totalPages.value)

    // 恢复阅读进度
    restoreReadingProgress()
  }
}, { immediate: true })



// 优化的PDF文件加载函数
const loadPdfFile = async (filePath: string): Promise<ArrayBuffer> => {
  console.log('开始加载PDF文件:', filePath)

  try {
    // 重置加载状态
    loadingProgress.value = 0
    loadingMessage.value = '正在读取PDF文件...'
    clearError()

    // 通过主进程获取文件的ArrayBuffer数据
    updateLoadingProgress(10, '正在读取文件数据...')
    const result = await window.electronAPI.file.readPdf(filePath)

    if (!result.success) {
      // 根据错误类型设置具体的错误信息
      const errorMsg = result.error || '获取文件数据失败'
      if (errorMsg.includes('ENOENT') || errorMsg.includes('not found')) {
        throw { type: 'FILE_NOT_FOUND', message: '找不到指定的PDF文件' }
      } else if (errorMsg.includes('EACCES') || errorMsg.includes('permission')) {
        throw { type: 'PERMISSION_DENIED', message: '没有权限访问该文件' }
      } else if (errorMsg.includes('EMFILE') || errorMsg.includes('too many')) {
        throw { type: 'TOO_MANY_FILES', message: '打开的文件过多，请关闭其他文件后重试' }
      } else {
        throw { type: 'FILE_READ_ERROR', message: errorMsg }
      }
    }

    // 设置文件大小并检查
    fileSize.value = result.size
    console.log('成功获取PDF数据，大小:', result.size, '字节')

    // 检查文件大小
    if (!checkFileSize(result.size)) {
      // 用户可以选择继续或取消
      const shouldContinue = await ElMessageBox.confirm(
        '文件过大可能导致性能问题，是否继续加载？',
        '文件大小警告',
        {
          confirmButtonText: '继续加载',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(() => false)

      if (!shouldContinue) {
        throw { type: 'FILE_TOO_LARGE', message: '用户取消加载大文件' }
      }
    }

    updateLoadingProgress(50, '正在处理PDF数据...')

    // 验证PDF文件格式
    const uint8Array = new Uint8Array(result.data)
    if (uint8Array.length < 4 ||
        String.fromCharCode(...uint8Array.slice(0, 4)) !== '%PDF') {
      throw { type: 'UNSUPPORTED_FORMAT', message: '文件不是有效的PDF格式' }
    }

    // 对于大文件，添加延迟以显示进度
    if (result.size > 10 * 1024 * 1024) { // 10MB以上
      await new Promise(resolve => setTimeout(resolve, 100))
      updateLoadingProgress(80, '正在准备PDF渲染...')
    }

    return result.data

  } catch (error: any) {
    console.error('加载PDF文件失败:', error)
    loadingProgress.value = 0

    // 根据错误类型设置错误信息
    if (error.type) {
      setError(error.message, error.type, error.stack)
    } else if (error.message) {
      setError(error.message, 'UNKNOWN', error.stack)
    } else {
      setError('未知错误', 'UNKNOWN', String(error))
    }

    throw error
  }
}



// PDF加载状态监听（包含内存管理）
watch(pdfFilePath, async (newPath, oldPath) => {
  console.log('PDF文件路径变化:', oldPath, '->', newPath)

  // 如果是切换文档，先清理旧文档的资源
  if (oldPath && oldPath !== newPath) {
    console.log('检测到文档切换，清理旧文档资源')
    await cleanupDocumentResources()
  }

  if (newPath) {
    try {
      isLoading.value = true
      clearError()
      loadingProgress.value = 0
      console.log('开始加载PDF文档:', newPath)

      // 统一使用ArrayBuffer方式加载，vue3-pdf-app会自动处理
      const arrayBuffer = await loadPdfFile(newPath)

      updateLoadingProgress(90, '正在初始化PDF查看器...')
      pdfSource.value = arrayBuffer

      console.log('PDF文档加载成功，大小:', arrayBuffer.byteLength, '字节')
      updateLoadingProgress(100, 'PDF加载完成')

    } catch (err: any) {
      console.error('PDF加载失败:', err)
      // 错误已经在loadPdfFile中处理，这里只需要设置加载状态
      isLoading.value = false
      loadingProgress.value = 0
    }
  }
})

/**
 * 清理文档切换时的资源
 */
const cleanupDocumentResources = async () => {
  console.log('开始清理文档资源...')

  // 清理页面缓存
  pageRenderCache.value.clear()
  visiblePages.value.clear()
  renderQueue.value = []

  // 重置PDF相关状态
  pdfSource.value = null
  currentPage.value = 1
  totalPages.value = 0

  // 强制垃圾回收（如果可用）
  if (window.gc) {
    window.gc()
    console.log('执行垃圾回收')
  }

  // 等待一帧，确保DOM更新
  await nextTick()

  // 更新内存使用量
  updateMemoryUsage()

  console.log('文档资源清理完成')
}



// ==================== vue3-pdf-app事件处理 ====================

/**
 * PDF应用创建完成事件处理
 * 当vue3-pdf-app组件初始化完成时触发
 * @param pdfApplication vue3-pdf-app的应用实例，包含pdfViewer、pdfDocument等核心对象
 */
const onPdfAppCreated = (pdfApplication: any) => {
  console.log('PDF应用创建完成:', pdfApplication)
  // 保存应用实例引用，用于后续调用其API（搜索、缩放、旋转等）
  pdfApp.value = pdfApplication

  // 在这里可以进行PDF应用的初始化配置
  // 例如：设置默认缩放、注册事件监听器等
  if (pdfApplication.pdfViewer) {
    // 监听页面变化事件，用于同步当前页码
    pdfApplication.pdfViewer.eventBus.on('pagechanging', (evt: any) => {
      currentPage.value = evt.pageNumber
    })
  }
}

const onPdfOpen = (pdfApplication: any) => {
  console.log('PDF文档打开:', pdfApplication)

  // 获取总页数
  if (pdfApplication.pdfDocument) {
    totalPages.value = pdfApplication.pdfDocument.numPages
    console.log('PDF总页数:', totalPages.value)

    // 根据页面数量决定是否启用虚拟滚动
    if (totalPages.value > virtualScrollConfig.value.renderThreshold) {
      console.log(`检测到大文档 (${totalPages.value} 页)，启用虚拟滚动优化`)
      virtualScrollConfig.value.enabled = true
    }
  }

  // 文档加载完成，隐藏加载状态
  isLoading.value = false
  loadingProgress.value = 100

  // 初始化性能优化
  if (virtualScrollConfig.value.enabled) {
    // 预加载当前页面周围的页面
    preloadAdjacentPages(currentPage.value)

    // 开始性能监控
    updatePerformanceMetrics()

    // 启动内存监控
    startMemoryMonitoring()
  }
}

// 处理PDF密码保护
const handlePasswordRequest = async (callback: (password: string) => void, reason: string) => {
  try {
    // 根据不同的原因显示不同的提示信息
    let title = '输入密码'
    let message = '此PDF文档需要密码'

    if (reason === 'INCORRECT_PASSWORD') {
      title = '密码错误'
      message = '密码错误，请重新输入正确的密码'
    } else if (reason === 'NEED_PASSWORD') {
      title = '需要密码'
      message = '此PDF文档受密码保护，请输入密码以继续'
    }

    const { value: password } = await ElMessageBox.prompt(
      message,
      title,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputPlaceholder: '请输入PDF密码',
        inputValidator: (value: string) => {
          if (!value || value.trim().length === 0) {
            return '密码不能为空'
          }
          return true
        },
        inputErrorMessage: '请输入有效的密码'
      }
    )

    callback(password || '')
    console.log('用户输入密码，长度:', password?.length || 0)

  } catch (error) {
    console.log('用户取消密码输入')
    setError('用户取消输入密码', 'PASSWORD_CANCELLED')
    callback('') // 传递空密码表示取消
  }
}

const onPagesRendered = (pdfApplication: any) => {
  console.log('PDF页面渲染完成:', pdfApplication)
  // 页面渲染完成后的处理
  if (pdfApplication.pdfViewer) {
    // 设置初始缩放
    setTimeout(() => {
      if (pdfApplication.pdfViewer.currentScaleValue !== scale.value) {
        pdfApplication.pdfViewer.currentScaleValue = scale.value
      }
    }, 100)
  }
}

// 基础方法
const goBack = () => {
  emit('go-back')
}

// 页面导航方法（支持性能优化）
const previousPage = () => {
  if (currentPage.value > 1) {
    const newPage = currentPage.value - 1
    navigateToPage(newPage)
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    const newPage = currentPage.value + 1
    navigateToPage(newPage)
  }
}

const goToPage = (pageNum: number) => {
  if (pageNum >= 1 && pageNum <= totalPages.value) {
    navigateToPage(pageNum)
  }
}

/**
 * 统一的页面导航方法，包含性能优化
 * @param pageNum 目标页码
 */
const navigateToPage = (pageNum: number) => {
  // 记录页面切换开始时间
  startTime.value = Date.now()
  const oldPage = currentPage.value
  currentPage.value = pageNum

  // 通过vue3-pdf-app的API进行页面跳转
  if (pdfApp.value?.pdfViewer) {
    pdfApp.value.pdfViewer.currentPageNumber = currentPage.value
  }

  // 更新阅读进度
  updateProgress()

  // 如果启用了性能优化模式，进行相关处理
  if (virtualScrollConfig.value.enabled) {
    // 更新可见页面集合
    visiblePages.value.clear()
    visiblePages.value.add(pageNum)

    // 预加载相邻页面
    preloadAdjacentPages(pageNum)

    // 更新页面缓存的访问时间
    if (pageRenderCache.value.has(pageNum)) {
      const cacheEntry = pageRenderCache.value.get(pageNum)!
      cacheEntry.lastAccessTime = Date.now()
    }

    // 更新性能指标
    updatePerformanceMetrics()
  }

  console.log(`页面导航: ${oldPage} -> ${pageNum}`)
}

const goToPageFromInput = () => {
  const pageNum = parseInt(pageInput.value)
  if (pageNum && pageNum >= 1 && pageNum <= totalPages.value) {
    goToPage(pageNum)
  } else {
    // 恢复当前页码
    pageInput.value = currentPage.value.toString()
  }
}

// 缩放功能
const zoomIn = () => {
  const newScale = Math.min(scale.value * 1.2, 5.0)
  scale.value = newScale
  // 通过vue3-pdf-app的API设置缩放
  if (pdfApp.value?.pdfViewer) {
    pdfApp.value.pdfViewer.currentScaleValue = newScale
  }
  fitParent.value = false
}

const zoomOut = () => {
  const newScale = Math.max(scale.value / 1.2, 0.1)
  scale.value = newScale
  // 通过vue3-pdf-app的API设置缩放
  if (pdfApp.value?.pdfViewer) {
    pdfApp.value.pdfViewer.currentScaleValue = newScale
  }
  fitParent.value = false
}

const resetZoom = () => {
  scale.value = 1.0
  // 通过vue3-pdf-app的API设置缩放
  if (pdfApp.value?.pdfViewer) {
    pdfApp.value.pdfViewer.currentScaleValue = 1.0
  }
  fitParent.value = false
}

// 页面旋转
const rotatePage = () => {
  rotation.value = (rotation.value + 90) % 360
  // 通过vue3-pdf-app的API设置旋转
  if (pdfApp.value?.pdfViewer) {
    pdfApp.value.pdfViewer.pagesRotation = rotation.value
  }
}

// 适应功能处理
const handleFitCommand = (command: string) => {
  if (!pdfApp.value?.pdfViewer) return

  switch (command) {
    case 'width':
      fitToWidth()
      break
    case 'page':
      fitToPage()
      break
    case 'actual':
      actualSize()
      break
  }
}

const fitToWidth = () => {
  // 通过vue3-pdf-app的API设置适应宽度
  if (pdfApp.value?.pdfViewer) {
    pdfApp.value.pdfViewer.currentScaleValue = 'page-width'
  }
  fitParent.value = true
}

const fitToPage = () => {
  // 通过vue3-pdf-app的API设置适应页面
  if (pdfApp.value?.pdfViewer) {
    pdfApp.value.pdfViewer.currentScaleValue = 'page-fit'
  }
  fitParent.value = true
}

const actualSize = () => {
  scale.value = 1.0
  // 通过vue3-pdf-app的API设置实际大小
  if (pdfApp.value?.pdfViewer) {
    pdfApp.value.pdfViewer.currentScaleValue = 'page-actual'
  }
  fitParent.value = false
}

// 侧边栏控制
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

// 搜索功能
const toggleSearch = () => {
  showSearch.value = !showSearch.value
  if (!showSearch.value) {
    // 关闭搜索时清除搜索结果
    clearSearchResults()
  }
}

const performSearch = () => {
  if (!searchQuery.value.trim()) {
    clearSearchResults()
    return
  }

  // 通过vue3-pdf-app的API执行搜索
  if (pdfApp.value?.pdfFindController) {
    pdfApp.value.pdfFindController.executeCommand('find', {
      query: searchQuery.value,
      caseSensitive: false,
      entireWord: false,
      highlightAll: true,
      findPrevious: false
    })
  }
}

const searchNext = () => {
  if (pdfApp.value?.pdfFindController) {
    pdfApp.value.pdfFindController.executeCommand('findagain', {
      query: searchQuery.value,
      caseSensitive: false,
      entireWord: false,
      highlightAll: true,
      findPrevious: false
    })
  }
}

const searchPrevious = () => {
  if (pdfApp.value?.pdfFindController) {
    pdfApp.value.pdfFindController.executeCommand('findagain', {
      query: searchQuery.value,
      caseSensitive: false,
      entireWord: false,
      highlightAll: true,
      findPrevious: true
    })
  }
}

const clearSearchResults = () => {
  searchResults.value = []
  currentSearchIndex.value = 0
  if (pdfApp.value?.pdfFindController) {
    pdfApp.value.pdfFindController.executeCommand('findbarclose')
  }
}

const onSearchInput = () => {
  // 防抖搜索 - 对大文档使用更长的延迟
  const debounceTime = totalPages.value > 100 ? 500 : 300

  if (searchInputTimer.value) {
    clearTimeout(searchInputTimer.value)
  }
  searchInputTimer.value = setTimeout(() => {
    if (searchQuery.value.trim()) {
      performSearch()
    } else {
      clearSearchResults()
    }
  }, debounceTime)
}

// 优化的搜索功能，支持大文档
const performSearchOptimized = async () => {
  if (!searchQuery.value.trim()) {
    clearSearchResults()
    return
  }

  // 对大文档显示搜索进度
  if (totalPages.value > 50) {
    ElMessage.info('正在搜索大文档，请稍候...')
  }

  try {
    // 通过vue3-pdf-app的API执行搜索
    if (pdfApp.value?.pdfFindController) {
      pdfApp.value.pdfFindController.executeCommand('find', {
        query: searchQuery.value,
        caseSensitive: false,
        entireWord: false,
        highlightAll: true,
        findPrevious: false
      })
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  }
}

// 主题切换
const toggleTheme = () => {
  pdfTheme.value = pdfTheme.value === 'light' ? 'dark' : 'light'
  console.log('切换PDF主题为:', pdfTheme.value)
}

// 文件大小格式化
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 检查文件大小并给出警告
const checkFileSize = (size: number): boolean => {
  const maxSize = 100 * 1024 * 1024 // 100MB
  const warningSize = 20 * 1024 * 1024 // 20MB
  const largeFileSize = 50 * 1024 * 1024 // 50MB

  if (size > maxSize) {
    ElMessage.warning(`文件过大 (${formatFileSize(size)})，可能影响性能。建议使用较小的PDF文件。`)
    return false
  } else if (size > largeFileSize) {
    ElMessage.info(`检测到大文件 (${formatFileSize(size)})，将启用性能优化模式。`)
    // 启用虚拟滚动和缓存优化
    enablePerformanceMode(size)
  } else if (size > warningSize) {
    ElMessage.info(`文件较大 (${formatFileSize(size)})，加载可能需要一些时间。`)
  }

  return true
}

// ==================== 大文件性能优化方法 ====================

/**
 * 启用性能优化模式
 * @param fileSize 文件大小
 */
const enablePerformanceMode = (fileSize: number) => {
  console.log('启用大文件性能优化模式，文件大小:', formatFileSize(fileSize))

  // 根据文件大小调整缓存策略
  if (fileSize > 80 * 1024 * 1024) { // 80MB以上
    virtualScrollConfig.value.pageBufferSize = 2
    virtualScrollConfig.value.maxCacheSize = 6
  } else if (fileSize > 50 * 1024 * 1024) { // 50MB以上
    virtualScrollConfig.value.pageBufferSize = 3
    virtualScrollConfig.value.maxCacheSize = 8
  }

  virtualScrollConfig.value.enabled = true
  console.log('性能优化配置:', virtualScrollConfig.value)
}

/**
 * 页面渲染缓存管理
 */
const managePageCache = () => {
  const cache = pageRenderCache.value
  const maxSize = virtualScrollConfig.value.maxCacheSize

  // 如果缓存超过最大大小，清理最久未使用的页面
  if (cache.size > maxSize) {
    const sortedPages = Array.from(cache.keys()).sort((a, b) => {
      const aTime = cache.get(a)?.lastAccessTime || 0
      const bTime = cache.get(b)?.lastAccessTime || 0
      return aTime - bTime
    })

    // 删除最久未使用的页面
    const pagesToRemove = sortedPages.slice(0, cache.size - maxSize)
    pagesToRemove.forEach(pageNum => {
      cache.delete(pageNum)
      console.log('清理缓存页面:', pageNum)
    })
  }
}

/**
 * 预加载相邻页面
 * @param currentPageNum 当前页码
 */
const preloadAdjacentPages = (currentPageNum: number) => {
  if (!virtualScrollConfig.value.enabled) return

  const bufferSize = virtualScrollConfig.value.pageBufferSize
  const startPage = Math.max(1, currentPageNum - bufferSize)
  const endPage = Math.min(totalPages.value, currentPageNum + bufferSize)

  // 添加需要预加载的页面到队列
  for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
    if (!pageRenderCache.value.has(pageNum) && !renderQueue.value.includes(pageNum)) {
      renderQueue.value.push(pageNum)
    }
  }

  // 处理渲染队列
  processRenderQueue()
}

/**
 * 处理页面渲染队列
 */
const processRenderQueue = async () => {
  if (isRenderingPage.value || renderQueue.value.length === 0) return

  isRenderingPage.value = true

  try {
    while (renderQueue.value.length > 0) {
      const pageNum = renderQueue.value.shift()!

      // 检查页面是否仍然需要渲染
      if (Math.abs(pageNum - currentPage.value) <= virtualScrollConfig.value.pageBufferSize) {
        await renderPageToCache(pageNum)
      }

      // 避免阻塞UI，每渲染一页后暂停一下
      await new Promise(resolve => setTimeout(resolve, 10))
    }
  } finally {
    isRenderingPage.value = false
  }
}

/**
 * 将页面渲染到缓存
 * @param pageNum 页码
 */
const renderPageToCache = async (pageNum: number): Promise<void> => {
  const renderStartTime = performance.now()

  try {
    // 这里应该调用vue3-pdf-app的API来渲染特定页面
    // 由于vue3-pdf-app的限制，我们主要通过配置来优化性能
    const cacheEntry = {
      pageNumber: pageNum,
      rendered: true,
      lastAccessTime: Date.now(),
      renderTime: performance.now() - startTime
    }

    pageRenderCache.value.set(pageNum, cacheEntry)
    console.log(`页面 ${pageNum} 已缓存，渲染时间: ${cacheEntry.renderTime.toFixed(2)}ms`)

    // 管理缓存大小
    managePageCache()

  } catch (error) {
    console.error(`页面 ${pageNum} 渲染失败:`, error)
  }
}

/**
 * 更新性能指标
 */
const updatePerformanceMetrics = () => {
  const now = Date.now()
  const cache = pageRenderCache.value

  // 计算缓存命中率
  const totalRequests = cache.size + renderQueue.value.length
  const cacheHits = cache.size
  performanceMetrics.value.cacheHitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0

  // 更新内存使用量
  updateMemoryUsage()

  performanceMetrics.value.lastUpdateTime = now

  console.log('性能指标更新:', performanceMetrics.value)
}

// ==================== 内存管理和监控方法 ====================

/**
 * 启动内存监控
 */
const startMemoryMonitoring = () => {
  if (memoryMonitorTimer.value) {
    clearInterval(memoryMonitorTimer.value)
  }

  memoryMonitorConfig.value.enabled = true
  memoryLeakDetection.value.enabled = true
  memoryLeakDetection.value.baselineMemory = getCurrentMemoryUsage()

  memoryMonitorTimer.value = setInterval(() => {
    updateMemoryUsage()
    checkMemoryLeak()
  }, memoryMonitorConfig.value.checkInterval)

  console.log('内存监控已启动')
}

/**
 * 停止内存监控
 */
const stopMemoryMonitoring = () => {
  if (memoryMonitorTimer.value) {
    clearInterval(memoryMonitorTimer.value)
    memoryMonitorTimer.value = null
  }

  memoryMonitorConfig.value.enabled = false
  memoryLeakDetection.value.enabled = false

  console.log('内存监控已停止')
}

/**
 * 获取当前内存使用量
 */
const getCurrentMemoryUsage = (): number => {
  // 基于缓存大小和文件大小估算内存使用量
  const cacheMemory = pageRenderCache.value.size * 2 // 每页约2MB
  const baseMemory = fileSize.value ? fileSize.value / (1024 * 1024) * 0.1 : 0 // 文件基础内存占用
  const totalMemory = cacheMemory + baseMemory

  return Math.round(totalMemory * 100) / 100 // 保留两位小数
}

/**
 * 更新内存使用量
 */
const updateMemoryUsage = () => {
  const currentUsage = getCurrentMemoryUsage()
  performanceMetrics.value.memoryUsage = currentUsage

  // 添加到历史记录
  memoryHistory.value.push({
    timestamp: Date.now(),
    usage: currentUsage,
    cacheSize: pageRenderCache.value.size
  })

  // 限制历史记录数量
  if (memoryHistory.value.length > memoryMonitorConfig.value.maxMemoryHistory) {
    memoryHistory.value.shift()
  }

  // 检查内存警告
  checkMemoryWarnings(currentUsage)
}

/**
 * 检查内存警告
 */
const checkMemoryWarnings = (currentUsage: number) => {
  const config = memoryMonitorConfig.value

  if (currentUsage > config.criticalThreshold) {
    ElMessage.error(`内存使用过高 (${currentUsage.toFixed(1)}MB)，建议立即清理缓存`)
    // 自动触发内存清理
    triggerMemoryCleanup()
  } else if (currentUsage > config.warningThreshold) {
    ElMessage.warning(`内存使用较高 (${currentUsage.toFixed(1)}MB)，建议清理缓存`)
  }
}

/**
 * 检查内存泄漏
 */
const checkMemoryLeak = () => {
  if (!memoryLeakDetection.value.enabled) return

  const currentUsage = performanceMetrics.value.memoryUsage
  const baseline = memoryLeakDetection.value.baselineMemory
  const detection = memoryLeakDetection.value

  // 如果内存使用量持续增长
  if (currentUsage > baseline * 1.1) { // 超过基准10%
    detection.consecutiveIncreases++

    if (detection.consecutiveIncreases >= detection.leakThreshold) {
      console.warn('检测到可能的内存泄漏:', {
        baseline: baseline.toFixed(1),
        current: currentUsage.toFixed(1),
        increases: detection.consecutiveIncreases
      })

      ElMessage.warning('检测到内存持续增长，可能存在内存泄漏')

      // 重置检测状态
      detection.consecutiveIncreases = 0
      detection.baselineMemory = currentUsage
    }
  } else {
    // 重置连续增长计数
    detection.consecutiveIncreases = 0
  }
}

// 优化的PDF加载进度处理
const updateLoadingProgress = (progress: number, message?: string) => {
  loadingProgress.value = Math.min(100, Math.max(0, progress))
  if (message) {
    loadingMessage.value = message
  }
}

// ==================== 错误处理和恢复机制 ====================

/**
 * 设置错误状态，包含详细的错误信息和解决建议
 * @param errorMessage 错误消息
 * @param errorType 错误类型
 * @param details 错误详细信息
 */
const setError = (errorMessage: string, errorType: string = 'UNKNOWN', details?: string) => {
  error.value = errorMessage
  errorDetails.value = details || ''

  // 根据错误类型设置标题和建议
  switch (errorType) {
    case 'NETWORK_ERROR':
      errorTitle.value = '网络连接错误'
      errorSuggestions.value = [
        '检查网络连接是否正常',
        '确认PDF文件URL是否可访问',
        '尝试刷新页面重新加载'
      ]
      break

    case 'FILE_CORRUPTED':
      errorTitle.value = 'PDF文件损坏'
      errorSuggestions.value = [
        '确认PDF文件是否完整',
        '尝试使用其他PDF阅读器打开文件',
        '重新下载或获取PDF文件'
      ]
      break

    case 'UNSUPPORTED_FORMAT':
      errorTitle.value = '不支持的文件格式'
      errorSuggestions.value = [
        '确认文件是有效的PDF格式',
        '检查文件扩展名是否为.pdf',
        '尝试转换文件格式后重新上传'
      ]
      break

    case 'PASSWORD_REQUIRED':
      errorTitle.value = 'PDF密码验证失败'
      errorSuggestions.value = [
        '确认输入的密码是否正确',
        '联系文档提供者获取正确密码',
        '检查是否有大小写敏感问题'
      ]
      break

    case 'FILE_TOO_LARGE':
      errorTitle.value = '文件过大'
      errorSuggestions.value = [
        '尝试使用较小的PDF文件',
        '关闭其他应用程序释放内存',
        '考虑将大文件分割为多个小文件'
      ]
      break

    case 'PERMISSION_DENIED':
      errorTitle.value = '文件访问权限不足'
      errorSuggestions.value = [
        '检查文件是否被其他程序占用',
        '确认有足够的文件访问权限',
        '尝试以管理员身份运行应用'
      ]
      break

    default:
      errorTitle.value = 'PDF加载失败'
      errorSuggestions.value = [
        '检查PDF文件是否存在且可访问',
        '确认文件格式正确',
        '尝试重新启动应用程序'
      ]
  }

  console.error(`PDF错误 [${errorType}]:`, errorMessage, details)
}

/**
 * 清除错误状态
 */
const clearError = () => {
  error.value = ''
  errorTitle.value = ''
  errorDetails.value = ''
  errorSuggestions.value = []
}

/**
 * 重试加载PDF文档
 */
const retryLoadPdf = async () => {
  if (!pdfFilePath.value) {
    ElMessage.warning('没有可重试的PDF文件')
    return
  }

  try {
    isRetrying.value = true
    clearError()

    // 重置状态
    isLoading.value = true
    loadingProgress.value = 0
    pdfSource.value = null

    // 延迟一下再重试，给用户反馈
    await new Promise(resolve => setTimeout(resolve, 500))

    // 重新触发加载
    const currentPath = pdfFilePath.value
    pdfFilePath.value = ''
    await nextTick()
    pdfFilePath.value = currentPath

    console.log('重试加载PDF:', currentPath)

  } catch (err: any) {
    console.error('重试加载失败:', err)
    setError('重试加载失败', 'RETRY_FAILED', err.message)
  } finally {
    isRetrying.value = false
  }
}

/**
 * 选择不同的文件
 */
const selectDifferentFile = () => {
  // 发出事件让父组件处理文件选择
  emit('select-file')
  ElMessage.info('请选择其他PDF文件')
}

/**
 * 报告错误
 */
const reportError = () => {
  const errorReport = {
    title: errorTitle.value,
    message: error.value,
    details: errorDetails.value,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  }

  // 复制错误信息到剪贴板
  navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2)).then(() => {
    ElMessage.success('错误信息已复制到剪贴板，您可以发送给技术支持')
  }).catch(() => {
    ElMessage.info('请手动复制以下错误信息发送给技术支持：\n' + JSON.stringify(errorReport, null, 2))
  })
}

// 工具命令处理
const handleToolCommand = (command: string) => {
  switch (command) {
    case 'print':
      printPdf()
      break
    case 'download':
      downloadPdf()
      break
    case 'fitWidth':
      fitToWidth()
      break
    case 'fitPage':
      fitToPage()
      break
    case 'actualSize':
      actualSize()
      break
  }
}

// 打印功能
const printPdf = () => {
  try {
    // 通过vue3-pdf-app的API执行打印
    if (pdfApp.value?.pdfViewer) {
      // 使用PDF.js的打印功能
      window.print()
    } else {
      // 降级到浏览器打印
      window.print()
    }
    console.log('执行PDF打印')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败')
  }
}

// 下载功能
const downloadPdf = async () => {
  try {
    if (download && currentBook.value) {
      // 使用usePDF提供的下载功能
      download({
        filename: currentBook.value.title + '.pdf'
      })
      ElMessage.success('PDF下载成功')
    } else if (pdfFilePath.value && currentBook.value) {
      // 降级到文件复制方式
      const result = await window.electronAPI.file.saveAs({
        defaultPath: currentBook.value.title + '.pdf',
        filters: [
          { name: 'PDF Files', extensions: ['pdf'] }
        ]
      })

      if (result.success && result.filePath) {
        await window.electronAPI.file.copyFile(pdfFilePath.value, result.filePath)
        ElMessage.success('PDF下载成功')
      }
    }
  } catch (error) {
    console.error('下载PDF失败:', error)
    ElMessage.error('下载失败')
  }
}



// 书签功能
const addBookmark = async () => {
  try {
    const title = await ElMessageBox.prompt('请输入书签标题', '添加书签', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValue: `第 ${currentPage.value} 页`
    })

    if (title.value && props.bookId) {
      const bookmark = await bookmarkManager.addBookmark(props.bookId, {
        title: title.value,
        pageNumber: currentPage.value,
        position: {
          x: 0,
          y: 0
        }
      })

      // 重新加载书签列表
      await loadBookmarks()
      ElMessage.success('书签添加成功')
    }
  } catch (error) {
    console.error('添加书签失败:', error)
    ElMessage.error('添加书签失败')
  }
}

const goToBookmark = (bookmark: any) => {
  goToPage(bookmark.pageNumber)
}

// 格式化日期
const formatDate = (date: Date) => {
  if (!date) return ''
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

const removeBookmark = async (bookmark: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个书签吗？', '删除书签', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await bookmarkManager.removeBookmark(props.bookId, bookmark.id)
    await loadBookmarks()
    ElMessage.success('书签删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除书签失败:', error)
      ElMessage.error('删除书签失败')
    }
  }
}







// PDF事件处理
const onPageLoaded = (pageInfo: any) => {
  console.log('页面加载完成:', pageInfo)
  isLoading.value = false

  // 记录渲染时间
  if (startTime.value > 0) {
    performanceMetrics.value.renderTime = Date.now() - startTime.value
    console.log('页面渲染时间:', performanceMetrics.value.renderTime, 'ms')
  }

  // 页面加载完成后的处理
  if (totalPages.value === 0) {
    // 如果还没有获取到总页数，尝试从PDF对象获取
    // 注意：vue3-pdf-app的loaded事件只提供当前页面信息
    // 总页数需要从PDF应用实例中获取
  }

  // 监控内存使用情况（如果支持）
  if ('memory' in performance) {
    performanceMetrics.value.memoryUsage = (performance as any).memory.usedJSHeapSize
  }
}

const onTextLoaded = (textData: any) => {
  console.log('文本层加载完成:', textData)
  // 文本层加载完成，可以进行文本搜索等操作
}

const onAnnotationLoaded = (annotations: any) => {
  console.log('注释层加载完成:', annotations)
  // 注释层加载完成
}

const onHighlight = (highlightData: any) => {
  console.log('文本高亮:', highlightData)
  // 更新搜索结果
  if (highlightData && highlightData.matches) {
    searchResults.value = highlightData.matches
  }
}

const onAnnotation = (annotationEvent: any) => {
  console.log('注释交互:', annotationEvent)

  // 处理不同类型的注释事件
  switch (annotationEvent.type) {
    case 'internal-link':
      // 内部链接，跳转到指定页面
      if (annotationEvent.data.referencedPage) {
        goToPage(annotationEvent.data.referencedPage)
      }
      break
    case 'link':
      // 外部链接
      if (annotationEvent.data.url) {
        window.open(annotationEvent.data.url, '_blank')
      }
      break
    case 'file-attachment':
      // 文件附件
      handleFileAttachment(annotationEvent.data)
      break
    default:
      // 其他注释类型
      break
  }
}

const handleFileAttachment = (attachmentData: any) => {
  try {
    const { filename, content } = attachmentData
    const blob = new Blob([content])
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
    ElMessage.success(`附件 ${filename} 下载成功`)
  } catch (error) {
    console.error('下载附件失败:', error)
    ElMessage.error('下载附件失败')
  }
}



const updateProgress = async () => {
  if (window.electronAPI?.reader?.updateProgress && totalPages.value > 0) {
    const progress = (currentPage.value / totalPages.value) * 100
    await window.electronAPI.reader.updateProgress(props.bookId, progress, currentPage.value)
  }
}

const retryLoad = () => {
  error.value = ''
  loadPdfDocument()
}

const restoreReadingProgress = async () => {
  try {
    // 获取阅读进度
    const progress = await window.electronAPI.reader.getProgress(props.bookId)
    if (progress && progress.currentPage && progress.currentPage <= totalPages.value) {
      currentPage.value = progress.currentPage
    }
  } catch (error) {
    console.error('恢复阅读进度失败:', error)
  }
}

const loadPdfDocument = async () => {
  try {
    console.log('开始加载PDF文档，书籍ID:', props.bookId)
    isLoading.value = true
    error.value = ''

    // 获取书籍信息
    const book = await window.electronAPI.reader.getBook(props.bookId)
    console.log('获取到书籍信息:', book)

    if (!book) {
      throw new Error('书籍不存在')
    }

    // 设置PDF文件路径
    const filePath = book.file_path || book.filePath
    console.log('设置PDF文件路径:', filePath)

    if (!filePath) {
      throw new Error('PDF文件路径为空')
    }

    pdfFilePath.value = filePath

    // 加载书签
    await loadBookmarks()

    console.log('PDF文档路径设置完成:', pdfFilePath.value)

  } catch (err) {
    console.error('加载PDF失败:', err)
    error.value = `加载失败: ${err.message || err}`
    isLoading.value = false
  }
}

const loadBookmarks = async () => {
  try {
    if (props.bookId) {
      const loadedBookmarks = await bookmarkManager.getBookmarks(props.bookId)
      bookmarks.value = loadedBookmarks
      console.log('加载书签成功，共', loadedBookmarks.length, '个书签')
    }
  } catch (error) {
    console.error('加载书签失败:', error)
    bookmarks.value = []
  }
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // 防止在输入框中触发快捷键
  if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
    return
  }

  switch (event.key) {
    case 'ArrowLeft':
    case 'PageUp':
      event.preventDefault()
      previousPage()
      break
    case 'ArrowRight':
    case 'PageDown':
    case ' ': // 空格键
      event.preventDefault()
      nextPage()
      break
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case '0':
      event.preventDefault()
      resetZoom()
      break
    case 'f':
    case 'F':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        toggleSearch()
      }
      break
    case 'Escape':
      if (showSearch.value) {
        event.preventDefault()
        toggleSearch()
      }
      break
    case 's':
    case 'S':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        toggleSidebar()
      }
      break
    case 'r':
    case 'R':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        rotatePage()
      }
      break
    case 'p':
    case 'P':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        printPdf()
      }
      break
    case 'd':
    case 'D':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        downloadPdf()
      }
      break
  }
}

// Store
const readerStore = useReaderStore()

// 计算属性
const {
  currentBook
} = readerStore

// 计算阅读进度文本
const progressText = computed(() => {
  if (totalPages.value === 0) return ''
  const progress = Math.round((currentPage.value / totalPages.value) * 100)
  return `${progress}% (${currentPage.value}/${totalPages.value})`
})

// 监听当前页变化，自动更新进度和页面输入框
watch(currentPage, (newPage) => {
  pageInput.value = newPage.toString()
  updateProgress()
})

// 生命周期
onMounted(() => {
  loadPdfDocument()
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 内存清理功能
const cleanupMemory = () => {
  // 清理搜索结果
  searchResults.value = []
  searchQuery.value = ''

  // 清理书签缓存
  bookmarks.value = []

  // 建议垃圾回收（如果支持）
  if (window.gc) {
    window.gc()
  }

  console.log('内存清理完成')
}

onUnmounted(() => {
  // 清理资源
  document.removeEventListener('keydown', handleKeydown)
  if (searchInputTimer.value) {
    clearTimeout(searchInputTimer.value)
  }

  // 清理PDF设置监听器
  pdfSettings.removeListener(() => {})

  // 清理性能优化相关资源
  cleanupPerformanceResources()

  // 执行内存清理
  cleanupMemory()

  console.log('PDF阅读器组件已卸载，所有资源已清理')
})

/**
 * 清理性能优化相关资源
 */
const cleanupPerformanceResources = () => {
  // 停止内存监控
  stopMemoryMonitoring()

  // 清理页面渲染缓存
  pageRenderCache.value.clear()
  visiblePages.value.clear()
  renderQueue.value = []

  // 清理内存历史记录
  memoryHistory.value = []

  // 重置性能配置
  virtualScrollConfig.value.enabled = false
  performanceMetrics.value = {
    memoryUsage: 0,
    renderTime: 0,
    cacheHitRate: 0,
    lastUpdateTime: Date.now()
  }

  // 重置内存监控配置
  memoryMonitorConfig.value.enabled = false
  memoryLeakDetection.value = {
    enabled: false,
    baselineMemory: 0,
    consecutiveIncreases: 0,
    leakThreshold: 5,
    lastCleanupTime: Date.now()
  }

  console.log('性能优化资源已清理')
}

/**
 * 手动触发内存清理
 */
const triggerMemoryCleanup = () => {
  // 清理超出缓存大小限制的页面
  managePageCache()

  // 清理不在可见范围内的页面缓存
  const currentPageNum = currentPage.value
  const bufferSize = virtualScrollConfig.value.pageBufferSize
  const minPage = Math.max(1, currentPageNum - bufferSize)
  const maxPage = Math.min(totalPages.value, currentPageNum + bufferSize)

  const pagesToRemove: number[] = []
  pageRenderCache.value.forEach((_, pageNum) => {
    if (pageNum < minPage || pageNum > maxPage) {
      pagesToRemove.push(pageNum)
    }
  })

  pagesToRemove.forEach(pageNum => {
    pageRenderCache.value.delete(pageNum)
  })

  // 更新性能指标
  updatePerformanceMetrics()

  ElMessage.success(`已清理 ${pagesToRemove.length} 个页面缓存，释放内存`)
  console.log('手动内存清理完成，清理页面:', pagesToRemove)
}

/**
 * 获取内存状态样式类
 */
const getMemoryStatusClass = () => {
  const usage = performanceMetrics.value.memoryUsage
  const config = memoryMonitorConfig.value

  if (usage > config.criticalThreshold) {
    return 'memory-critical'
  } else if (usage > config.warningThreshold) {
    return 'memory-warning'
  }
  return 'memory-normal'
}

/**
 * 获取内存趋势
 */
const getMemoryTrend = () => {
  if (memoryHistory.value.length < 2) return '稳定'

  const recent = memoryHistory.value.slice(-3) // 最近3次记录
  const first = recent[0].usage
  const last = recent[recent.length - 1].usage
  const diff = last - first

  if (Math.abs(diff) < 1) return '稳定'
  return diff > 0 ? `↗ +${diff.toFixed(1)}MB` : `↘ ${diff.toFixed(1)}MB`
}

/**
 * 获取内存趋势样式类
 */
const getMemoryTrendClass = () => {
  const trend = getMemoryTrend()
  if (trend.includes('↗')) return 'trend-up'
  if (trend.includes('↘')) return 'trend-down'
  return 'trend-stable'
}

/**
 * 切换内存监控状态
 */
const toggleMemoryMonitoring = () => {
  if (memoryMonitorConfig.value.enabled) {
    stopMemoryMonitoring()
    ElMessage.info('内存监控已停止')
  } else {
    startMemoryMonitoring()
    ElMessage.success('内存监控已启动')
  }
}
</script>

<style scoped>
.pdf-reader-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-height: 56px;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-center {
  flex: 1;
  justify-content: center;
}

.book-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.book-title {
  font-weight: 600;
  color: #333;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reading-progress {
  font-size: 12px;
  color: #666;
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-separator {
  color: #666;
  margin: 0 4px;
}

.total-pages {
  color: #666;
  font-size: 14px;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-level {
  min-width: 50px;
  text-align: center;
  font-size: 12px;
  color: #666;
}

.tool-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-bar {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 16px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 600px;
  margin: 0 auto;
}

.search-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-results {
  font-size: 12px;
  color: #666;
  min-width: 60px;
  text-align: center;
}

.content-area {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.sidebar-tabs {
  display: flex;
  padding: 8px;
  gap: 4px;
  border-bottom: 1px solid #e0e0e0;
}

.thumbnails-panel,
.bookmarks-panel {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.thumbnail-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.thumbnail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.thumbnail-item:hover {
  background: #f0f0f0;
}

.thumbnail-item.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.thumbnail-page {
  width: 60px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.bookmark-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.bookmark-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: background 0.2s;
}

.bookmark-item:hover {
  background: #e6f7ff;
}

.bookmark-content {
  flex: 1;
  cursor: pointer;
  padding: 4px;
}

.bookmark-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
}

.bookmark-page {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.bookmark-date {
  font-size: 11px;
  color: #999;
}

.bookmark-actions-inline {
  display: flex;
  align-items: center;
}

.empty-bookmarks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #999;
  text-align: center;
}

.empty-bookmarks .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-bookmarks p {
  margin: 0;
  font-size: 14px;
}

.bookmark-actions {
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
  margin-top: 16px;
}

.pdf-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
  background: #e5e5e5;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  color: #666;
}

.loading-container .el-icon {
  font-size: 32px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  max-width: 300px;
  text-align: center;
}

.loading-progress {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #999;
}

.file-info {
  font-size: 12px;
  color: #999;
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.error-container .error-icon {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 16px;
}

.error-content {
  max-width: 600px;
  text-align: center;
}

.error-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.error-message {
  font-size: 16px;
  color: #606266;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.error-details {
  margin: 20px 0;
  text-align: left;
}

.error-stack {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #909399;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin: 24px 0;
  flex-wrap: wrap;
}

.error-suggestions {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 6px;
  padding: 16px;
  margin-top: 20px;
  text-align: left;
}

.error-suggestions h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
}

.error-suggestions ul {
  margin: 0;
  padding-left: 20px;
}

.error-suggestions li {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}

.pdf-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.vue-pdf-component {
  max-width: 100%;
  max-height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  background: white;
}



/* PDF文本层样式 */
:deep(.textLayer) {
  opacity: 0.2;
}

:deep(.textLayer:hover) {
  opacity: 1;
}

/* PDF高亮样式 */
:deep(.highlight) {
  background-color: rgba(255, 255, 0, 0.3);
  border-radius: 2px;
}

/* PDF注释层样式 */
:deep(.annotationLayer) {
  opacity: 0.9;
}

/* VuePDF组件样式覆盖 */
:deep(.vue-pdf-app) {
  width: 100%;
  height: 100%;
}

:deep(.vue-pdf-app canvas) {
  max-width: 100%;
  height: auto;
}

/* 滚动条样式 */
.pdf-viewer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.pdf-viewer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.pdf-viewer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.pdf-viewer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px;
  }

  .toolbar-center {
    order: 3;
    width: 100%;
    justify-content: center;
  }

  .book-info {
    order: 2;
    width: 100%;
  }

  .sidebar {
    width: 250px;
  }

  .thumbnail-list {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  }

  .search-container {
    flex-direction: column;
    gap: 8px;
  }

  .search-controls {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .toolbar-left,
  .toolbar-right {
    flex-direction: column;
    gap: 4px;
  }

  .page-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .zoom-controls,
  .tool-controls {
    flex-wrap: wrap;
  }
}

/* vue3-pdf-app组件样式 */
.vue-pdf-app-component {
  width: 100%;
  height: 100%;
  border: none;
  background: #f5f5f5;
}

/* 自定义工具栏样式 */
.custom-toolbar-left,
.custom-toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
}

.page-separator {
  color: #666;
  font-size: 14px;
}

.total-pages {
  color: #666;
  font-size: 14px;
  min-width: 20px;
}

/* 搜索栏样式 */
.search-bar {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 8px 16px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 8px;
  max-width: 600px;
}

.search-controls {
  flex-shrink: 0;
}

.search-results-info {
  color: #666;
  font-size: 12px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 性能监控样式 */
.performance-monitor {
  padding: 12px;
  min-width: 200px;
}

.monitor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 6px 0;
  font-size: 12px;
}

.monitor-item span:first-child {
  color: #606266;
}

.monitor-item span:last-child {
  font-weight: 600;
  color: #409eff;
}

/* 内存状态样式 */
.memory-normal {
  color: #67c23a !important;
}

.memory-warning {
  color: #e6a23c !important;
}

.memory-critical {
  color: #f56c6c !important;
}

/* 内存趋势样式 */
.trend-up {
  color: #f56c6c !important;
}

.trend-down {
  color: #67c23a !important;
}

.trend-stable {
  color: #909399 !important;
}

/* 状态指示器 */
.status-indicator {
  font-size: 14px;
}

.status-indicator.warning {
  animation: pulse 2s infinite;
}

.status-indicator.normal {
  opacity: 0.8;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 监控操作按钮 */
.monitor-actions {
  display: flex;
  gap: 4%;
  justify-content: space-between;
}
</style>
