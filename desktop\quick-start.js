/**
 * Yu Reader 快速启动脚本
 * 跳过有问题的渲染进程构建，直接启动应用
 */

const { spawn } = require('child_process')
const { existsSync } = require('fs')
const path = require('path')

console.log('🚀 Yu Reader 快速启动...')

// 检查必要文件
const mainFile = path.join(__dirname, 'dist/main/app.js')
const preloadFile = path.join(__dirname, 'dist/preload/index.js')

if (!existsSync(mainFile)) {
  console.log('❌ 主进程文件不存在，正在构建...')
  const buildMain = spawn('npm', ['run', 'build:main'], { 
    stdio: 'inherit', 
    shell: true,
    cwd: __dirname
  })
  
  buildMain.on('close', (code) => {
    if (code === 0) {
      console.log('✅ 主进程构建完成')
      checkPreload()
    } else {
      console.log('❌ 主进程构建失败')
      process.exit(1)
    }
  })
} else {
  console.log('✅ 主进程文件存在')
  checkPreload()
}

function checkPreload() {
  if (!existsSync(preloadFile)) {
    console.log('❌ 预加载文件不存在，正在构建...')
    const buildPreload = spawn('npm', ['run', 'build:preload'], { 
      stdio: 'inherit', 
      shell: true,
      cwd: __dirname
    })
    
    buildPreload.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 预加载脚本构建完成')
        startElectron()
      } else {
        console.log('❌ 预加载脚本构建失败')
        process.exit(1)
      }
    })
  } else {
    console.log('✅ 预加载文件存在')
    startElectron()
  }
}

function startElectron() {
  console.log('🚀 启动 Electron 应用...')
  
  // 启动开发服务器（如果还没运行）
  const devServer = spawn('npm', ['run', 'dev:renderer'], {
    stdio: 'pipe',
    shell: true,
    cwd: __dirname
  })
  
  // 等待开发服务器启动
  setTimeout(() => {
    const electron = spawn('npx', ['electron', '.'], {
      stdio: 'inherit',
      shell: true,
      cwd: __dirname,
      env: { ...process.env, NODE_ENV: 'development' }
    })
    
    electron.on('close', (code) => {
      console.log(`Electron 应用退出，代码: ${code}`)
      devServer.kill()
      process.exit(code)
    })
    
    electron.on('error', (err) => {
      console.error('启动 Electron 失败:', err)
      devServer.kill()
      process.exit(1)
    })
  }, 3000)
}
