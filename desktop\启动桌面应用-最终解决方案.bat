@echo off
chcp 65001 >nul
title Yu Reader 桌面应用启动器

echo.
echo ========================================
echo    🚀 Yu Reader 桌面应用启动器
echo    最终解决方案
echo ========================================
echo.

REM 设置环境变量
set NODE_ENV=development
set ELECTRON_ENABLE_LOGGING=1

echo 🔍 环境检查...
echo - NODE_ENV: %NODE_ENV%
echo - 当前目录: %CD%

REM 检查必要文件
if not exist "dist\main\app.js" (
    echo ❌ 主进程文件不存在
    echo 💡 正在构建主进程...
    call npm run build:main
    if errorlevel 1 (
        echo ❌ 主进程构建失败
        pause
        exit /b 1
    )
)

if not exist "node_modules\electron\dist\electron.exe" (
    echo ❌ Electron不存在
    echo 💡 正在安装Electron...
    call npm install electron --save-dev
    if errorlevel 1 (
        echo ❌ Electron安装失败
        pause
        exit /b 1
    )
)

echo ✅ 文件检查通过

REM 检查开发服务器
echo 🔍 检查开发服务器...
netstat -an | find "5173" >nul
if errorlevel 1 (
    echo ⚠️ 开发服务器未运行，正在启动...
    start "Yu Reader Dev Server" cmd /c "npm run dev:renderer"
    echo ⏳ 等待开发服务器启动...
    timeout /t 15 /nobreak >nul
) else (
    echo ✅ 开发服务器已运行
)

echo.
echo 🚀 尝试启动桌面应用...
echo.

REM 方法1: 使用npx
echo 📱 方法1: 使用npx启动...
start "Yu Reader Desktop" cmd /k "echo 启动Yu Reader... && npx electron . && echo 应用已退出 && pause"

REM 等待5秒
timeout /t 5 /nobreak >nul

REM 检查是否启动成功
tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
if not errorlevel 1 (
    echo ✅ 桌面应用启动成功！
    echo 🎉 Yu Reader 桌面应用正在运行
    goto :success
)

REM 方法2: 直接启动可执行文件
echo 📱 方法2: 直接启动可执行文件...
start "Yu Reader Desktop" cmd /k "echo 启动Yu Reader... && node_modules\electron\dist\electron.exe . && echo 应用已退出 && pause"

REM 等待5秒
timeout /t 5 /nobreak >nul

REM 再次检查
tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
if not errorlevel 1 (
    echo ✅ 桌面应用启动成功！
    echo 🎉 Yu Reader 桌面应用正在运行
    goto :success
)

REM 如果都失败了，提供浏览器版本
echo ⚠️ 桌面应用启动可能失败
echo 🌐 在浏览器中打开应用作为备选方案...
start http://127.0.0.1:5173/

echo.
echo ========================================
echo    ✅ 应用已在浏览器中启动
echo    📱 URL: http://127.0.0.1:5173/
echo ========================================
echo.
echo 💡 浏览器版本功能说明:
echo    - 包含所有桌面端功能
echo    - 支持PDF阅读器增强功能
echo    - 智能错误处理和性能优化
echo    - 内存管理和监控
echo.
echo 🔧 桌面应用故障排除建议:
echo    1. 检查Windows防火墙设置
echo    2. 检查杀毒软件是否阻止了应用
echo    3. 尝试以管理员身份运行此脚本
echo    4. 重新安装依赖: npm install
echo    5. 检查任务管理器中是否有electron.exe进程
echo.
goto :end

:success
echo.
echo ========================================
echo    ✅ 桌面应用启动成功！
echo ========================================
echo.
echo 💡 使用说明:
echo    - 桌面应用包含完整的PDF阅读器增强功能
echo    - 支持智能错误处理和性能优化
echo    - 支持内存管理和监控
echo    - 点击📊图标查看性能监控面板
echo.

:end
echo 按任意键退出...
pause >nul
