@echo off
chcp 65001 >nul
title Yu Reader 桌面应用启动器

echo.
echo ========================================
echo    🚀 Yu Reader 桌面应用启动器
echo ========================================
echo.

REM 设置环境变量
set NODE_ENV=development

REM 检查必要文件
echo 🔍 检查必要文件...
if not exist "dist\main\app.js" (
    echo ❌ 主进程文件不存在: dist\main\app.js
    echo 💡 请先运行: npm run build:main
    pause
    exit /b 1
)

if not exist "node_modules\electron\dist\electron.exe" (
    echo ❌ Electron可执行文件不存在
    echo 💡 请确保Electron已正确安装
    pause
    exit /b 1
)

echo ✅ 必要文件检查通过

REM 检查开发服务器
echo 🔍 检查开发服务器...
netstat -an | find "5173" >nul
if errorlevel 1 (
    echo ⚠️ 开发服务器未运行，正在启动...
    start "Yu Reader Dev Server" cmd /c "npm run dev:renderer"
    echo ⏳ 等待开发服务器启动...
    timeout /t 10 /nobreak >nul
) else (
    echo ✅ 开发服务器已运行
)

REM 启动Electron桌面应用
echo 🚀 启动Electron桌面应用...

REM 方法1: 直接启动
echo 📱 尝试启动桌面应用...
start "Yu Reader Desktop" /D "%CD%" "node_modules\electron\dist\electron.exe" "."

REM 等待3秒检查是否启动成功
timeout /t 3 /nobreak >nul

REM 检查进程是否存在
tasklist /FI "IMAGENAME eq electron.exe" 2>nul | find /I "electron.exe" >nul
if not errorlevel 1 (
    echo ✅ 桌面应用启动成功！
    echo 🎉 Yu Reader 桌面应用正在运行
    echo.
    echo ========================================
    echo    ✅ 启动完成
    echo ========================================
    echo.
    echo 💡 如果应用窗口没有显示，请检查任务栏
    echo 📱 桌面应用包含完整的PDF阅读器增强功能
    goto :end
)

REM 如果桌面应用启动失败，提供浏览器备选方案
echo ⚠️ 桌面应用可能启动失败，提供备选方案...
echo.
echo 🌐 在浏览器中打开应用...
start http://127.0.0.1:5173/

echo.
echo ========================================
echo    ✅ 应用已在浏览器中启动
echo    📱 URL: http://127.0.0.1:5173/
echo ========================================
echo.
echo 💡 浏览器版本功能说明:
echo    - 包含所有桌面端功能
echo    - 支持PDF阅读器增强功能
echo    - 智能错误处理和性能优化
echo    - 内存管理和监控
echo    - 支持文件上传和处理
echo.
echo 🔧 桌面应用故障排除:
echo    - 检查防火墙和杀毒软件设置
echo    - 尝试以管理员身份运行
echo    - 重新安装依赖: npm install

:end
echo.
echo 按任意键退出...
pause >nul
