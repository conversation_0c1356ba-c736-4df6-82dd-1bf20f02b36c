/**
 * 最终的桌面应用启动脚本
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Yu Reader 桌面应用启动器');
console.log('==========================================');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 检查文件
const mainFile = path.join(__dirname, 'dist', 'main', 'app.js');
const electronExe = path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe');

console.log('📁 文件检查:');
console.log('- 主进程文件:', fs.existsSync(mainFile) ? '✅' : '❌', mainFile);
console.log('- Electron可执行文件:', fs.existsSync(electronExe) ? '✅' : '❌', electronExe);

if (!fs.existsSync(mainFile)) {
    console.error('❌ 主进程文件不存在');
    process.exit(1);
}

if (!fs.existsSync(electronExe)) {
    console.error('❌ Electron可执行文件不存在');
    process.exit(1);
}

// 检查开发服务器
console.log('\n🌐 检查开发服务器...');
const http = require('http');

const checkServer = () => {
    return new Promise((resolve) => {
        const req = http.get('http://127.0.0.1:5173/', (res) => {
            resolve(true);
        });
        req.on('error', () => {
            resolve(false);
        });
        req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
        });
    });
};

checkServer().then(serverRunning => {
    console.log('- 开发服务器状态:', serverRunning ? '✅ 运行中' : '❌ 未运行');
    
    if (!serverRunning) {
        console.log('⚠️ 开发服务器未运行，Electron应用可能无法正常显示');
        console.log('💡 建议先运行: npm run dev:renderer');
    }
    
    console.log('\n🚀 启动Electron应用...');
    
    // 方法1: 使用spawn
    console.log('📱 方法1: 使用spawn启动...');
    const child = spawn(electronExe, ['.'], {
        stdio: 'pipe',
        cwd: __dirname,
        env: {
            ...process.env,
            NODE_ENV: 'development'
        },
        detached: true
    });
    
    let hasOutput = false;
    
    child.stdout.on('data', (data) => {
        hasOutput = true;
        console.log('📤 stdout:', data.toString().trim());
    });
    
    child.stderr.on('data', (data) => {
        hasOutput = true;
        console.log('📤 stderr:', data.toString().trim());
    });
    
    child.on('error', (error) => {
        console.error('❌ spawn启动失败:', error.message);
        
        // 方法2: 使用exec作为备选
        console.log('\n📱 方法2: 使用exec启动...');
        const command = `"${electronExe}" "."`;
        console.log('执行命令:', command);
        
        exec(command, {
            cwd: __dirname,
            env: {
                ...process.env,
                NODE_ENV: 'development'
            }
        }, (error, stdout, stderr) => {
            if (error) {
                console.error('❌ exec启动失败:', error.message);
                console.log('\n💡 建议:');
                console.log('1. 检查防火墙和杀毒软件设置');
                console.log('2. 以管理员身份运行');
                console.log('3. 使用浏览器版本: http://127.0.0.1:5173/');
            } else {
                console.log('✅ exec启动成功');
                if (stdout) console.log('stdout:', stdout);
                if (stderr) console.log('stderr:', stderr);
            }
        });
    });
    
    child.on('close', (code, signal) => {
        console.log(`📱 进程退出 - 代码: ${code}, 信号: ${signal}`);
    });
    
    // 5秒后检查状态
    setTimeout(() => {
        if (child.exitCode === null && !child.killed) {
            console.log('✅ Electron进程正在运行 (PID:', child.pid, ')');
            console.log('🎉 桌面应用应该已经启动！');
        } else if (!hasOutput) {
            console.log('⚠️ Electron进程已退出，可能启动失败');
            console.log('💡 请尝试在浏览器中使用: http://127.0.0.1:5173/');
        }
    }, 5000);
    
    // 处理进程退出
    process.on('SIGINT', () => {
        console.log('\n🛑 收到退出信号，关闭Electron...');
        if (!child.killed) {
            child.kill('SIGINT');
        }
        process.exit(0);
    });
});
