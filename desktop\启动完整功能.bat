@echo off
chcp 65001 >nul
title Yu Reader 完整功能启动器

echo.
echo ========================================
echo    🚀 Yu Reader 完整功能启动器
echo ========================================
echo.

REM 设置环境变量
set NODE_ENV=development
set ELECTRON_IS_DEV=1

echo 🔍 当前目录: %CD%
echo 🔍 环境变量: NODE_ENV=%NODE_ENV%
echo.

REM 检查Node.js和npm环境
echo 📋 检查运行环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或不在 PATH 中
    echo 💡 请安装 Node.js 18+ 版本
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装或不在 PATH 中
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% 和 npm %NPM_VERSION% 环境正常

REM 检查依赖
echo.
echo 📦 检查项目依赖...
if not exist "node_modules" (
    echo ⚠️ 依赖未安装，正在安装...
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

REM 检查数据库目录
echo.
echo 🗄️ 检查数据库环境...
if not exist "database" (
    echo 📁 创建数据库目录...
    mkdir database
)
echo ✅ 数据库目录已准备

REM 检查必要文件并构建
echo.
echo 🔨 检查构建文件...
set NEED_BUILD=0

if not exist "dist\main\app.js" (
    echo ⚠️ 主进程文件不存在
    set NEED_BUILD=1
)

if not exist "dist\preload\index.js" (
    echo ⚠️ 预加载文件不存在
    set NEED_BUILD=1
)

if not exist "dist\renderer\index.html" (
    echo ⚠️ 渲染进程文件不存在
    set NEED_BUILD=1
)

if %NEED_BUILD%==1 (
    echo 🔨 正在构建项目...
    echo 💡 这可能需要几分钟时间，请耐心等待...
    call npm run build
    if errorlevel 1 (
        echo ❌ 项目构建失败
        echo 💡 尝试分步构建...
        call npm run build:main
        call npm run build:preload
        call npm run build:renderer
        if errorlevel 1 (
            echo ❌ 分步构建也失败，使用简化模式
            goto SIMPLE_MODE
        )
    )
    echo ✅ 项目构建完成
) else (
    echo ✅ 构建文件完整
)

REM 启动开发服务器
echo.
echo 🌐 启动开发服务器...
start "Yu Reader Dev Server" cmd /c "cd /d %CD% && npm run dev:renderer"
echo ⏳ 等待开发服务器启动...
timeout /t 5 /nobreak >nul

REM 启动完整功能应用
echo.
echo 🚀 启动 Yu Reader 完整功能版本...
echo 💡 包含数据库、图书管理、阅读功能
echo.

call npm run start:dev
if errorlevel 1 (
    echo ⚠️ 完整功能启动失败，尝试标准构建模式...
    call npm start
    if errorlevel 1 (
        echo ⚠️ 标准模式也失败，切换到简化模式...
        goto SIMPLE_MODE
    )
)

echo ✅ Yu Reader 完整功能版本已退出
goto END

:SIMPLE_MODE
echo.
echo 🔧 使用简化模式启动...
echo 💡 简化模式特点：
echo    - 基本界面功能正常
echo    - 主题切换正常
echo    - 图书管理功能受限
echo.
npx electron simple-start.js
if errorlevel 1 (
    echo ❌ 简化模式也启动失败
    pause
    exit /b 1
)
echo ✅ Yu Reader 简化模式已退出

:END
echo.
echo ✅ Yu Reader 应用已退出
echo 💡 如果需要重新启动，请再次运行此脚本
pause
