const { app, BrowserWindow } = require('electron')
const { join } = require('path')

console.log('🚀 Yu Reader 简化启动模式...')

let mainWindow = null

function createWindow() {
  console.log('创建主窗口...')
  
  const { screen } = require('electron')
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize
  
  const windowWidth = Math.min(1200, Math.floor(width * 0.8))
  const windowHeight = Math.min(800, Math.floor(height * 0.8))
  
  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: 800,
    minHeight: 600,
    show: true,
    frame: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, 'dist/preload/index.js'),
      webSecurity: false,
      allowRunningInsecureContent: true,
      sandbox: false,
      spellcheck: false
    }
  })

  // 检查环境变量
  const nodeEnv = (process.env.NODE_ENV || '').trim()
  console.log('环境变量 NODE_ENV:', `"${nodeEnv}"`)
  
  if (nodeEnv === 'development') {
    const devUrl = 'http://127.0.0.1:5173'
    console.log('开发模式，加载URL:', devUrl)
    
    mainWindow.loadURL(devUrl).catch(err => {
      console.error('加载开发服务器失败:', err)
      // 如果开发服务器加载失败，尝试加载构建文件
      const buildFile = join(__dirname, 'dist/renderer/index.html')
      console.log('尝试加载构建文件:', buildFile)
      mainWindow.loadFile(buildFile)
    })
    
    // 开发模式下打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    const buildFile = join(__dirname, 'dist/renderer/index.html')
    console.log('生产模式，加载文件:', buildFile)
    mainWindow.loadFile(buildFile)
  }

  mainWindow.on('closed', () => {
    console.log('主窗口已关闭')
    mainWindow = null
  })

  mainWindow.once('ready-to-show', () => {
    console.log('窗口准备显示')
    mainWindow.show()
    mainWindow.focus()
  })

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('页面加载失败:', errorCode, errorDescription, validatedURL)
  })

  console.log('主窗口创建完成')
  return mainWindow
}

app.whenReady().then(() => {
  console.log('Electron 应用已准备就绪')
  
  try {
    createWindow()
    console.log('应用启动成功')
  } catch (error) {
    console.error('应用启动失败:', error)
    app.quit()
  }
})

app.on('window-all-closed', () => {
  console.log('所有窗口已关闭')
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason)
})

console.log('简化启动脚本已加载')
