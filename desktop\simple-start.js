const { app, BrowserWindow, ipcMain } = require('electron')
const { join } = require('path')

console.log('🚀 Yu Reader 简化启动模式...')

let mainWindow = null

// 注册基本的IPC处理器
function registerBasicIPCHandlers() {
  console.log('注册基本IPC处理器...')

  // 图书列表处理器
  ipcMain.handle('book:list', async () => {
    console.log('收到图书列表请求')
    // 返回空列表，避免错误
    return []
  })

  // 其他基本处理器
  ipcMain.handle('book:add', async (event, bookData) => {
    console.log('收到添加图书请求:', bookData)
    return { success: false, message: '简化模式不支持添加图书' }
  })

  ipcMain.handle('book:delete', async (event, bookId) => {
    console.log('收到删除图书请求:', bookId)
    return { success: false, message: '简化模式不支持删除图书' }
  })

  ipcMain.handle('book:update', async (event, bookId, updateData) => {
    console.log('收到更新图书请求:', bookId, updateData)
    return { success: false, message: '简化模式不支持更新图书' }
  })

  // 文件处理器
  ipcMain.handle('file:read', async (event, filePath) => {
    console.log('收到文件读取请求:', filePath)
    return { success: false, message: '简化模式不支持文件读取' }
  })

  // 数据库处理器
  ipcMain.handle('database:check-book-paths', async () => {
    console.log('收到数据库路径检查请求')
    return { success: true, message: '简化模式跳过数据库检查' }
  })

  ipcMain.handle('database:fix-book-paths', async () => {
    console.log('收到数据库路径修复请求')
    return { success: true, message: '简化模式跳过数据库修复' }
  })

  // 阅读器处理器
  ipcMain.handle('reader:get-content', async (event, filePath) => {
    console.log('收到内容获取请求:', filePath)
    return { success: false, message: '简化模式不支持内容获取' }
  })

  // TXT阅读器处理器
  ipcMain.handle('txt:read', async (event, filePath) => {
    console.log('收到TXT读取请求:', filePath)
    return { success: false, message: '简化模式不支持TXT阅读' }
  })

  // EPUB阅读器处理器
  ipcMain.handle('epub:read', async (event, filePath) => {
    console.log('收到EPUB读取请求:', filePath)
    return { success: false, message: '简化模式不支持EPUB阅读' }
  })

  console.log('基本IPC处理器注册完成')
}

function createWindow() {
  console.log('创建主窗口...')
  
  const { screen } = require('electron')
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize
  
  const windowWidth = Math.min(1200, Math.floor(width * 0.8))
  const windowHeight = Math.min(800, Math.floor(height * 0.8))
  
  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: 800,
    minHeight: 600,
    show: true,
    frame: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, 'dist/preload/index.js'),
      webSecurity: false,
      allowRunningInsecureContent: true,
      sandbox: false,
      spellcheck: false
    }
  })

  // 检查环境变量
  const nodeEnv = (process.env.NODE_ENV || '').trim()
  console.log('环境变量 NODE_ENV:', `"${nodeEnv}"`)
  
  if (nodeEnv === 'development') {
    const devUrl = 'http://127.0.0.1:5173'
    console.log('开发模式，加载URL:', devUrl)
    
    mainWindow.loadURL(devUrl).catch(err => {
      console.error('加载开发服务器失败:', err)
      // 如果开发服务器加载失败，尝试加载构建文件
      const buildFile = join(__dirname, 'dist/renderer/index.html')
      console.log('尝试加载构建文件:', buildFile)
      mainWindow.loadFile(buildFile)
    })
    
    // 开发模式下打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    const buildFile = join(__dirname, 'dist/renderer/index.html')
    console.log('生产模式，加载文件:', buildFile)
    mainWindow.loadFile(buildFile)
  }

  mainWindow.on('closed', () => {
    console.log('主窗口已关闭')
    mainWindow = null
  })

  mainWindow.once('ready-to-show', () => {
    console.log('窗口准备显示')
    mainWindow.show()
    mainWindow.focus()
  })

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('页面加载失败:', errorCode, errorDescription, validatedURL)
  })

  console.log('主窗口创建完成')
  return mainWindow
}

app.whenReady().then(() => {
  console.log('Electron 应用已准备就绪')

  try {
    // 注册IPC处理器
    registerBasicIPCHandlers()

    // 创建窗口
    createWindow()
    console.log('应用启动成功')
  } catch (error) {
    console.error('应用启动失败:', error)
    app.quit()
  }
})

app.on('window-all-closed', () => {
  console.log('所有窗口已关闭')
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason)
})

console.log('简化启动脚本已加载')
