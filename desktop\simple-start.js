const { app, BrowserWindow, ipcMain } = require('electron')
const { join } = require('path')

console.log('🚀 Yu Reader 简化启动模式...')

let mainWindow = null

// 注册基本的IPC处理器
function registerBasicIPCHandlers() {
  console.log('注册基本IPC处理器...')

  // 图书列表处理器
  ipcMain.handle('book:list', async () => {
    console.log('📚 收到图书列表请求，准备返回示例数据...')

    const sampleBooks = [
      {
        id: '1',
        title: '示例图书 1 - 文学经典',
        author: '示例作者',
        file_format: 'txt',
        format: 'txt',
        file_size: 1024000,
        fileSize: 1024000,
        description: '这是一个示例图书，用于演示Yu Reader的界面功能。包含丰富的文学内容。',
        reading_status: 'unread',
        readingStatus: 'unread',
        current_page: 0,
        currentPage: 0,
        reading_progress_percent: 0,
        readProgress: 0,
        created_at: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        addedAt: new Date().toISOString(),
        lastReadAt: null,
        tags: ['文学', '经典'],
        language: 'zh-CN',
        publisher: '示例出版社',
        publishDate: '2024-01-01',
        isbn: '978-0000000000',
        coverPath: null,
        filePath: '/path/to/sample1.txt'
      },
      {
        id: '2',
        title: '示例图书 2 - 科幻小说',
        author: '另一个作者',
        file_format: 'epub',
        format: 'epub',
        file_size: 2048000,
        fileSize: 2048000,
        description: '这是第二个示例图书，展示不同格式的支持。精彩的科幻故事。',
        reading_status: 'reading',
        readingStatus: 'reading',
        current_page: 25,
        currentPage: 25,
        reading_progress_percent: 35,
        readProgress: 35,
        created_at: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        addedAt: new Date().toISOString(),
        lastReadAt: new Date().toISOString(),
        tags: ['科幻', '小说'],
        language: 'zh-CN',
        publisher: '科幻出版社',
        publishDate: '2024-02-01',
        isbn: '978-1111111111',
        coverPath: null,
        filePath: '/path/to/sample2.epub'
      },
      {
        id: '3',
        title: '示例图书 3 - 技术手册',
        author: '第三位作者',
        file_format: 'pdf',
        format: 'pdf',
        file_size: 5120000,
        fileSize: 5120000,
        description: '这是第三个示例图书，演示PDF格式支持。实用的技术指南。',
        reading_status: 'finished',
        readingStatus: 'finished',
        current_page: 100,
        currentPage: 100,
        reading_progress_percent: 100,
        readProgress: 100,
        created_at: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        addedAt: new Date().toISOString(),
        lastReadAt: new Date().toISOString(),
        tags: ['技术', '手册'],
        language: 'zh-CN',
        publisher: '技术出版社',
        publishDate: '2024-03-01',
        isbn: '978-2222222222',
        coverPath: null,
        filePath: '/path/to/sample3.pdf'
      }
    ]

    console.log(`📚 返回 ${sampleBooks.length} 本示例图书:`)
    sampleBooks.forEach((book, index) => {
      console.log(`  ${index + 1}. ${book.title} (${book.format}) - ${book.readingStatus}`)
    })

    return sampleBooks
  })

  // 其他基本处理器
  ipcMain.handle('book:add', async (event, bookData) => {
    console.log('收到添加图书请求:', bookData)
    return { success: false, message: '简化模式不支持添加图书' }
  })

  ipcMain.handle('book:delete', async (event, bookId) => {
    console.log('收到删除图书请求:', bookId)
    return { success: false, message: '简化模式不支持删除图书' }
  })

  ipcMain.handle('book:update', async (event, bookId, updateData) => {
    console.log('收到更新图书请求:', bookId, updateData)
    return { success: false, message: '简化模式不支持更新图书' }
  })

  // 文件处理器
  ipcMain.handle('file:read', async (event, filePath) => {
    console.log('收到文件读取请求:', filePath)
    return { success: false, message: '简化模式不支持文件读取' }
  })

  // 数据库处理器
  ipcMain.handle('database:check-book-paths', async () => {
    console.log('收到数据库路径检查请求')
    return { success: true, message: '简化模式跳过数据库检查' }
  })

  ipcMain.handle('database:fix-book-paths', async () => {
    console.log('收到数据库路径修复请求')
    return { success: true, message: '简化模式跳过数据库修复' }
  })

  // 阅读器处理器
  ipcMain.handle('reader:get-content', async (event, filePath) => {
    console.log('收到内容获取请求:', filePath)
    return { success: false, message: '简化模式不支持内容获取' }
  })

  // TXT阅读器处理器
  ipcMain.handle('txt:read', async (event, filePath) => {
    console.log('收到TXT读取请求:', filePath)
    return { success: false, message: '简化模式不支持TXT阅读' }
  })

  // EPUB阅读器处理器
  ipcMain.handle('epub:read', async (event, filePath) => {
    console.log('收到EPUB读取请求:', filePath)
    return { success: false, message: '简化模式不支持EPUB阅读' }
  })

  // 窗口操作处理器
  ipcMain.handle('window:minimize', async () => {
    console.log('收到窗口最小化请求')
    if (mainWindow) {
      mainWindow.minimize()
      return { success: true }
    }
    return { success: false }
  })

  ipcMain.handle('window:maximize', async () => {
    console.log('收到窗口最大化请求')
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize()
      } else {
        mainWindow.maximize()
      }
      return { success: true }
    }
    return { success: false }
  })

  ipcMain.handle('window:close', async () => {
    console.log('收到窗口关闭请求')
    if (mainWindow) {
      mainWindow.close()
      return { success: true }
    }
    return { success: false }
  })

  // 主题处理器
  ipcMain.handle('theme:change', async (event, theme) => {
    console.log('收到主题切换请求:', theme)
    return { success: true, theme }
  })

  console.log('基本IPC处理器注册完成')
}

function createWindow() {
  console.log('创建主窗口...')
  
  const { screen } = require('electron')
  const primaryDisplay = screen.getPrimaryDisplay()
  const { width, height } = primaryDisplay.workAreaSize
  
  const windowWidth = Math.min(1200, Math.floor(width * 0.8))
  const windowHeight = Math.min(800, Math.floor(height * 0.8))
  
  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: 800,
    minHeight: 600,
    show: true,
    frame: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: join(__dirname, 'dist/preload/index.js'),
      webSecurity: false, // 禁用web安全以支持阅读器功能
      allowRunningInsecureContent: true,
      sandbox: false,
      spellcheck: false,
      // 添加额外的安全设置以支持阅读器功能
      additionalArguments: [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--allow-running-insecure-content',
        '--disable-site-isolation-trials'
      ]
    }
  })

  // 强制使用开发模式
  console.log('🌐 强制使用开发服务器模式')

  const devUrl = 'http://127.0.0.1:5173'
  console.log('开发模式，加载URL:', devUrl)

  mainWindow.loadURL(devUrl).catch(err => {
    console.error('❌ 加载开发服务器失败:', err)
    console.log('⚠️ 开发服务器可能未启动，请先运行: npm run dev:renderer')

    const buildFile = join(__dirname, 'dist/renderer/index.html')
    console.log('🔄 尝试加载构建文件:', buildFile)
    mainWindow.loadFile(buildFile)
  })

  // 开发模式下打开开发者工具
  mainWindow.webContents.openDevTools()

  mainWindow.on('closed', () => {
    console.log('主窗口已关闭')
    mainWindow = null
  })

  mainWindow.once('ready-to-show', () => {
    console.log('窗口准备显示')
    mainWindow.show()
    mainWindow.focus()
  })

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('页面加载失败:', errorCode, errorDescription, validatedURL)
  })

  console.log('主窗口创建完成')
  return mainWindow
}

app.whenReady().then(() => {
  console.log('Electron 应用已准备就绪')

  try {
    // 注册IPC处理器
    registerBasicIPCHandlers()

    // 创建窗口
    createWindow()
    console.log('应用启动成功')
  } catch (error) {
    console.error('应用启动失败:', error)
    app.quit()
  }
})

app.on('window-all-closed', () => {
  console.log('所有窗口已关闭')
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason)
})

console.log('简化启动脚本已加载')
