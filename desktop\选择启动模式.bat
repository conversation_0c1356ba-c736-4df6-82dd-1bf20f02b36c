@echo off
chcp 65001 >nul
title Yu Reader 启动模式选择器

echo.
echo ========================================
echo    📚 Yu Reader 启动模式选择器
echo ========================================
echo.
echo 请选择启动模式：
echo.
echo 1. 🎯 混合模式 (推荐)
echo    - 包含4本精选示例图书
echo    - 完整的界面功能
echo    - 最佳用户体验
echo.
echo 2. 🔧 简化模式
echo    - 包含3本基础示例图书
echo    - 基本界面功能
echo    - 快速启动
echo.
echo 3. ⚙️ 标准模式 (实验性)
echo    - 尝试完整数据库功能
echo    - 可能需要额外配置
echo    - 高级用户使用
echo.
echo 4. 🚪 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto HYBRID_MODE
if "%choice%"=="2" goto SIMPLE_MODE
if "%choice%"=="3" goto STANDARD_MODE
if "%choice%"=="4" goto EXIT
echo 无效选择，请重新运行脚本
pause
exit /b 1

:HYBRID_MODE
echo.
echo 🎯 启动混合模式...
echo 💡 混合模式特点：
echo    - 《三体》- 科幻经典，阅读中 (45%%)
echo    - 《活着》- 文学经典，已完成
echo    - 《JavaScript高级程序设计》- 技术书籍，未读
echo    - 《百年孤独》- 魔幻现实主义，阅读中 (25%%)
echo.

REM 检查开发服务器
echo 🌐 检查开发服务器...
netstat -an | findstr "5173" >nul 2>&1
if errorlevel 1 (
    echo 🚀 启动开发服务器...
    start "Yu Reader Dev Server" cmd /c "cd /d %CD% && npm run dev:renderer"
    echo ⏳ 等待开发服务器启动...
    timeout /t 3 /nobreak >nul
)

npx electron hybrid-start.js
if errorlevel 1 (
    echo ❌ 混合模式启动失败
    pause
    exit /b 1
)
echo ✅ 混合模式已退出
goto END

:SIMPLE_MODE
echo.
echo 🔧 启动简化模式...
echo 💡 简化模式特点：
echo    - 示例图书 1 - 文学经典 (TXT)
echo    - 示例图书 2 - 科幻小说 (EPUB)
echo    - 示例图书 3 - 技术手册 (PDF)
echo.

REM 检查开发服务器
echo 🌐 检查开发服务器...
netstat -an | findstr "5173" >nul 2>&1
if errorlevel 1 (
    echo 🚀 启动开发服务器...
    start "Yu Reader Dev Server" cmd /c "cd /d %CD% && npm run dev:renderer"
    echo ⏳ 等待开发服务器启动...
    timeout /t 3 /nobreak >nul
)

npx electron simple-start.js
if errorlevel 1 (
    echo ❌ 简化模式启动失败
    pause
    exit /b 1
)
echo ✅ 简化模式已退出
goto END

:STANDARD_MODE
echo.
echo ⚙️ 启动标准模式...
echo 💡 标准模式特点：
echo    - 尝试连接真实数据库
echo    - 完整的图书管理功能
echo    - 需要完整的项目构建
echo.

REM 检查构建文件
if not exist "dist\main\app.js" (
    echo 🔨 构建主进程...
    call npm run build:main
)

if not exist "dist\preload\index.js" (
    echo 🔨 构建预加载脚本...
    call npm run build:preload
)

if not exist "dist\renderer\index.html" (
    echo 🔨 构建渲染进程...
    call npm run build:renderer
)

REM 检查开发服务器
echo 🌐 启动开发服务器...
start "Yu Reader Dev Server" cmd /c "cd /d %CD% && npm run dev:renderer"
timeout /t 5 /nobreak >nul

call npm run start:dev
if errorlevel 1 (
    echo ❌ 标准模式启动失败，尝试构建模式...
    call npm start
    if errorlevel 1 (
        echo ❌ 标准模式完全失败
        pause
        exit /b 1
    )
)
echo ✅ 标准模式已退出
goto END

:EXIT
echo 👋 再见！
exit /b 0

:END
echo.
echo ✅ Yu Reader 已退出
echo 💡 如需重新启动，请再次运行此脚本
echo.
pause
