/**
 * 立即启动桌面应用
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 立即启动Yu Reader桌面应用...');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 检查文件
const electronExe = path.join(__dirname, 'node_modules', 'electron', 'dist', 'electron.exe');
const mainFile = path.join(__dirname, 'dist', 'main', 'app.js');

console.log('检查文件:');
console.log('- Electron:', fs.existsSync(electronExe) ? '✅' : '❌');
console.log('- 主进程:', fs.existsSync(mainFile) ? '✅' : '❌');

if (!fs.existsSync(electronExe)) {
    console.error('❌ Electron不存在');
    process.exit(1);
}

if (!fs.existsSync(mainFile)) {
    console.error('❌ 主进程文件不存在');
    process.exit(1);
}

console.log('✅ 启动Electron...');

// 启动Electron
const child = spawn(electronExe, ['.'], {
    stdio: ['ignore', 'pipe', 'pipe'],
    cwd: __dirname,
    env: {
        ...process.env,
        NODE_ENV: 'development',
        ELECTRON_ENABLE_LOGGING: '1'
    },
    windowsHide: false
});

console.log('📱 Electron进程已启动，PID:', child.pid);

child.stdout.on('data', (data) => {
    console.log('📤 stdout:', data.toString());
});

child.stderr.on('data', (data) => {
    console.log('📤 stderr:', data.toString());
});

child.on('error', (error) => {
    console.error('❌ 启动错误:', error);
});

child.on('close', (code) => {
    console.log(`📱 应用退出，代码: ${code}`);
});

// 10秒后检查状态
setTimeout(() => {
    if (child.exitCode === null) {
        console.log('✅ Electron应用正在运行！');
        console.log('🎉 桌面应用应该已经显示！');
    } else {
        console.log('❌ Electron应用已退出');
    }
}, 10000);

// 保持进程运行
process.on('SIGINT', () => {
    console.log('\n🛑 关闭应用...');
    child.kill();
    process.exit(0);
});
