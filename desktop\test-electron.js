/**
 * 测试Electron是否能正常启动
 */

const { app, BrowserWindow } = require('electron');

console.log('🚀 测试Electron启动...');

app.whenReady().then(() => {
    console.log('✅ Electron应用已准备就绪');
    
    const win = new BrowserWindow({
        width: 800,
        height: 600,
        title: 'Electron测试窗口',
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });
    
    win.loadURL('data:text/html,<h1>Electron测试成功！</h1><p>如果您看到这个窗口，说明Electron可以正常工作。</p>');
    
    console.log('✅ 测试窗口已创建');
    
    win.on('closed', () => {
        console.log('📱 测试窗口已关闭');
    });
});

app.on('window-all-closed', () => {
    console.log('📱 所有窗口已关闭，退出应用');
    app.quit();
});

console.log('⏳ 等待Electron应用准备就绪...');
