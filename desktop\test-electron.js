const { app, BrowserWindow } = require('electron')

console.log('测试 Electron 启动...')

function createWindow() {
  console.log('创建测试窗口...')
  
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    show: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  win.loadURL('data:text/html,<h1>Yu Reader Electron 测试窗口</h1><p>如果你看到这个窗口，说明 Electron 环境正常！</p>')
  
  console.log('测试窗口已创建')
  
  win.on('closed', () => {
    console.log('测试窗口已关闭')
  })
  
  return win
}

app.whenReady().then(() => {
  console.log('Electron 应用已准备就绪')
  createWindow()
})

app.on('window-all-closed', () => {
  console.log('所有窗口已关闭')
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

console.log('Electron 测试脚本已加载')
